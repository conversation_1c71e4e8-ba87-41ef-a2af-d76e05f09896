"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  target?: string; // CSS selector for the element to highlight
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: 'click' | 'hover' | 'focus' | 'none';
  optional?: boolean;
  component?: ReactNode;
}

export interface OnboardingFlow {
  id: string;
  name: string;
  description: string;
  steps: OnboardingStep[];
  autoStart?: boolean;
  persistent?: boolean;
}

interface OnboardingContextType {
  // Current state
  isActive: boolean;
  currentFlow: OnboardingFlow | null;
  currentStepIndex: number;
  currentStep: OnboardingStep | null;
  
  // Flow management
  startFlow: (flowId: string) => void;
  stopFlow: () => void;
  pauseFlow: () => void;
  resumeFlow: () => void;
  
  // Step navigation
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (stepIndex: number) => void;
  skipStep: () => void;
  
  // Flow registration
  registerFlow: (flow: OnboardingFlow) => void;
  unregisterFlow: (flowId: string) => void;
  
  // Progress tracking
  markStepCompleted: (stepId: string) => void;
  markFlowCompleted: (flowId: string) => void;
  isStepCompleted: (stepId: string) => boolean;
  isFlowCompleted: (flowId: string) => boolean;
  
  // Settings
  settings: OnboardingSettings;
  updateSettings: (settings: Partial<OnboardingSettings>) => void;
}

interface OnboardingSettings {
  autoStart: boolean;
  showProgress: boolean;
  allowSkip: boolean;
  showTooltips: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast';
  theme: 'light' | 'dark' | 'auto';
}

const defaultSettings: OnboardingSettings = {
  autoStart: true,
  showProgress: true,
  allowSkip: true,
  showTooltips: true,
  animationSpeed: 'normal',
  theme: 'auto',
};

const OnboardingContext = createContext<OnboardingContextType | null>(null);

interface OnboardingProviderProps {
  children: ReactNode;
  initialSettings?: Partial<OnboardingSettings>;
}

export function OnboardingProvider({ 
  children, 
  initialSettings = {} 
}: OnboardingProviderProps) {
  const [isActive, setIsActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentFlow, setCurrentFlow] = useState<OnboardingFlow | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [registeredFlows, setRegisteredFlows] = useState<Map<string, OnboardingFlow>>(new Map());
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [completedFlows, setCompletedFlows] = useState<Set<string>>(new Set());
  const [settings, setSettings] = useState<OnboardingSettings>({
    ...defaultSettings,
    ...initialSettings,
  });

  // Load persisted data on mount
  useEffect(() => {
    try {
      const savedCompletedSteps = localStorage.getItem('onboarding-completed-steps');
      const savedCompletedFlows = localStorage.getItem('onboarding-completed-flows');
      const savedSettings = localStorage.getItem('onboarding-settings');

      if (savedCompletedSteps) {
        setCompletedSteps(new Set(JSON.parse(savedCompletedSteps)));
      }
      if (savedCompletedFlows) {
        setCompletedFlows(new Set(JSON.parse(savedCompletedFlows)));
      }
      if (savedSettings) {
        setSettings(prev => ({ ...prev, ...JSON.parse(savedSettings) }));
      }
    } catch (error) {
      console.warn('Failed to load onboarding data:', error);
    }
  }, []);

  // Persist data when it changes
  useEffect(() => {
    try {
      localStorage.setItem('onboarding-completed-steps', JSON.stringify([...completedSteps]));
    } catch (error) {
      console.warn('Failed to save completed steps:', error);
    }
  }, [completedSteps]);

  useEffect(() => {
    try {
      localStorage.setItem('onboarding-completed-flows', JSON.stringify([...completedFlows]));
    } catch (error) {
      console.warn('Failed to save completed flows:', error);
    }
  }, [completedFlows]);

  useEffect(() => {
    try {
      localStorage.setItem('onboarding-settings', JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save onboarding settings:', error);
    }
  }, [settings]);

  const currentStep = currentFlow?.steps[currentStepIndex] || null;

  const startFlow = (flowId: string) => {
    const flow = registeredFlows.get(flowId);
    if (!flow) {
      console.warn(`Onboarding flow "${flowId}" not found`);
      return;
    }

    if (completedFlows.has(flowId) && !flow.persistent) {
      console.log(`Onboarding flow "${flowId}" already completed`);
      return;
    }

    setCurrentFlow(flow);
    setCurrentStepIndex(0);
    setIsActive(true);
    setIsPaused(false);
  };

  const stopFlow = () => {
    setIsActive(false);
    setCurrentFlow(null);
    setCurrentStepIndex(0);
    setIsPaused(false);
  };

  const pauseFlow = () => {
    setIsPaused(true);
  };

  const resumeFlow = () => {
    setIsPaused(false);
  };

  const nextStep = () => {
    if (!currentFlow) return;

    if (currentStepIndex < currentFlow.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      // Flow completed
      markFlowCompleted(currentFlow.id);
      stopFlow();
    }
  };

  const previousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  };

  const goToStep = (stepIndex: number) => {
    if (!currentFlow) return;
    
    if (stepIndex >= 0 && stepIndex < currentFlow.steps.length) {
      setCurrentStepIndex(stepIndex);
    }
  };

  const skipStep = () => {
    if (settings.allowSkip) {
      nextStep();
    }
  };

  const registerFlow = (flow: OnboardingFlow) => {
    setRegisteredFlows(prev => new Map(prev).set(flow.id, flow));
  };

  const unregisterFlow = (flowId: string) => {
    setRegisteredFlows(prev => {
      const newMap = new Map(prev);
      newMap.delete(flowId);
      return newMap;
    });
  };

  const markStepCompleted = (stepId: string) => {
    setCompletedSteps(prev => new Set(prev).add(stepId));
  };

  const markFlowCompleted = (flowId: string) => {
    setCompletedFlows(prev => new Set(prev).add(flowId));
  };

  const isStepCompleted = (stepId: string) => {
    return completedSteps.has(stepId);
  };

  const isFlowCompleted = (flowId: string) => {
    return completedFlows.has(flowId);
  };

  const updateSettings = (newSettings: Partial<OnboardingSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const contextValue: OnboardingContextType = {
    isActive: isActive && !isPaused,
    currentFlow,
    currentStepIndex,
    currentStep,
    startFlow,
    stopFlow,
    pauseFlow,
    resumeFlow,
    nextStep,
    previousStep,
    goToStep,
    skipStep,
    registerFlow,
    unregisterFlow,
    markStepCompleted,
    markFlowCompleted,
    isStepCompleted,
    isFlowCompleted,
    settings,
    updateSettings,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (!context) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
