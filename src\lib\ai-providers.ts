/**
 * Multi-Provider AI Service for TutorAI
 * Production-grade implementation with comprehensive error handling, monitoring, and caching
 */

import type { AgentEvent } from '@/lib/ag-ui-types';
import { prisma } from '@/lib/database';

// Provider interfaces
export interface AIProvider {
  name: 'openai' | 'anthropic' | 'groq' | 'openrouter';
  apiKey: string;
  model: string;
  baseUrl?: string;
  priority: number;
  maxRequestsPerMinute: number;
  maxTokensPerRequest: number;
  costPer1kTokens: number;
  isHealthy: boolean;
  lastHealthCheck: Date;
  errorCount: number;
  successCount: number;
  avgResponseTime: number;
}

export interface StreamingResponse {
  content: string;
  done: boolean;
  error?: string;
}

export interface AIProviderConfig {
  providers: AIProvider[];
  defaultProvider: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
  enableCaching: boolean;
  cacheExpiryMinutes: number;
  enableLoadBalancing: boolean;
  enableUsageQuotas: boolean;
  maxDailyUsagePerUser: number;
  enableMetrics: boolean;
}

export interface CacheEntry {
  key: string;
  response: string;
  timestamp: Date;
  provider: string;
  model: string;
  expiresAt: Date;
}

export interface UsageQuota {
  userId: string;
  dailyTokens: number;
  dailyCost: number;
  monthlyTokens: number;
  monthlyCost: number;
  lastReset: Date;
}

export interface ProviderMetrics {
  provider: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  totalTokens: number;
  totalCost: number;
  lastUpdated: Date;
}

export class MultiProviderAIService {
  private config: AIProviderConfig;
  private eventListeners: ((event: AgentEvent) => void)[] = [];
  private responseCache: Map<string, CacheEntry> = new Map();
  private usageQuotas: Map<string, UsageQuota> = new Map();
  private providerMetrics: Map<string, ProviderMetrics> = new Map();
  private requestCounts: Map<string, { count: number; resetTime: Date }> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private checkCircuitBreaker: (provider: AIProvider) => boolean;

  constructor(config: AIProviderConfig) {
    this.config = config;
    this.initializeProviders();
    this.startHealthMonitoring();
    this.checkCircuitBreaker = this.checkCircuitBreaker.bind(this);
    this.startCacheCleanup();
    this.loadUsageQuotas();
  }

  private async initializeProviders(): Promise<void> {
    for (const provider of this.config.providers) {
      // Initialize provider metrics
      this.providerMetrics.set(provider.name, {
        provider: provider.name,
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        avgResponseTime: 0,
        totalTokens: 0,
        totalCost: 0,
        lastUpdated: new Date()
      });

      // Initialize request counts for rate limiting
      this.requestCounts.set(provider.name, {
        count: 0,
        resetTime: new Date(Date.now() + 60000) // Reset every minute
      });

      // Perform initial health check
      await this.checkProviderHealth(provider);
    }
  }

  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      for (const provider of this.config.providers) {
        await this.checkProviderHealth(provider);
      }
    }, 60000); // Check every minute
  }

  private async checkProviderHealth(provider: AIProvider): Promise<void> {
    const startTime = Date.now();
    try {
      // Simple health check with minimal token usage
      const testPrompt = "Hi";
      const messageId = `health_${Date.now()}`;
      
      await this.makeProviderRequest(provider, testPrompt, messageId, {
        maxTokens: 5,
        temperature: 0,
        isHealthCheck: true
      });

      provider.isHealthy = true;
      provider.lastHealthCheck = new Date();
      provider.avgResponseTime = (provider.avgResponseTime + (Date.now() - startTime)) / 2;
      provider.errorCount = Math.max(0, provider.errorCount - 1); // Gradually reduce error count
      
      this.logProviderEvent(provider.name, 'health_check_success', {
        responseTime: Date.now() - startTime
      });
    } catch (error) {
      provider.isHealthy = false;
      provider.errorCount += 1;
      provider.lastHealthCheck = new Date();
      
      this.logProviderEvent(provider.name, 'health_check_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCount: provider.errorCount
      });
    }
  }

  private startCacheCleanup(): void {
    setInterval(() => {
      const now = new Date();
      for (const [key, entry] of Array.from(this.responseCache.entries())) {
        if (entry.expiresAt < now) {
          this.responseCache.delete(key);
        }
      }
    }, 300000); // Clean up every 5 minutes
  }

  private async loadUsageQuotas(): Promise<void> {
    if (!this.config.enableUsageQuotas) return;

    try {
      // Load usage quotas from database
      const quotas = await prisma.$queryRaw<any[]>`
        SELECT 
          "userId",
          SUM(CASE WHEN DATE(timestamp) = CURRENT_DATE THEN tokens ELSE 0 END) as "dailyTokens",
          SUM(CASE WHEN DATE(timestamp) = CURRENT_DATE THEN cost ELSE 0 END) as "dailyCost",
          SUM(CASE WHEN DATE_TRUNC('month', timestamp) = DATE_TRUNC('month', CURRENT_DATE) THEN tokens ELSE 0 END) as "monthlyTokens",
          SUM(CASE WHEN DATE_TRUNC('month', timestamp) = DATE_TRUNC('month', CURRENT_DATE) THEN cost ELSE 0 END) as "monthlyCost"
        FROM ai_usage 
        GROUP BY "userId"
      `;

      for (const quota of quotas) {
        this.usageQuotas.set(quota.userId, {
          userId: quota.userId,
          dailyTokens: parseInt(quota.dailyTokens) || 0,
          dailyCost: parseFloat(quota.dailyCost) || 0,
          monthlyTokens: parseInt(quota.monthlyTokens) || 0,
          monthlyCost: parseFloat(quota.monthlyCost) || 0,
          lastReset: new Date()
        });
      }
    } catch (error) {
      console.error('Failed to load usage quotas:', error);
    }
  }

  private generateCacheKey(prompt: string, options: any): string {
    const key = JSON.stringify({
      prompt: prompt.substring(0, 200), // Limit key size
      provider: options.provider,
      model: options.model,
      maxTokens: options.maxTokens,
      temperature: options.temperature
    });
    return Buffer.from(key).toString('base64');
  }

  private getCachedResponse(cacheKey: string): string | null {
    if (!this.config.enableCaching) return null;

    const entry = this.responseCache.get(cacheKey);
    if (entry && entry.expiresAt > new Date()) {
      this.logEvent('cache_hit', { cacheKey });
      return entry.response;
    }

    if (entry) {
      this.responseCache.delete(cacheKey);
    }
    return null;
  }

  private setCachedResponse(cacheKey: string, response: string, provider: string, model: string): void {
    if (!this.config.enableCaching) return;

    const expiresAt = new Date(Date.now() + this.config.cacheExpiryMinutes * 60000);
    this.responseCache.set(cacheKey, {
      key: cacheKey,
      response,
      timestamp: new Date(),
      provider,
      model,
      expiresAt
    });
  }

  private async checkUsageQuota(userId: string, estimatedTokens: number): Promise<boolean> {
    if (!this.config.enableUsageQuotas || !userId) return true;

    const quota = this.usageQuotas.get(userId);
    if (!quota) return true;

    // Check daily limit
    if (quota.dailyTokens + estimatedTokens > this.config.maxDailyUsagePerUser) {
      this.logEvent('quota_exceeded', {
        userId,
        dailyTokens: quota.dailyTokens,
        estimatedTokens,
        limit: this.config.maxDailyUsagePerUser
      });
      return false;
    }

    return true;
  }

  private selectOptimalProvider(excludeProviders: string[] = []): AIProvider | null {
    if (!this.config.enableLoadBalancing) {
      const defaultProvider = this.config.providers.find(p => p.name === this.config.defaultProvider);
      return defaultProvider?.isHealthy && defaultProvider.apiKey && this.checkCircuitBreaker(defaultProvider) ? defaultProvider : null;
    }

    // Filter healthy providers not in exclude list and check circuit breakers
    const availableProviders = this.config.providers
      .filter(p => 
        p.isHealthy && 
        p.apiKey && // Only consider providers with API keys
        !excludeProviders.includes(p.name) && 
        this.checkCircuitBreaker(p)
      )
      .sort((a, b) => {
        // Sort by priority first, then by performance metrics
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // Higher priority first
        }
        
        // Consider error rate, response time, and circuit breaker state
        const aPerf = this.performanceMetrics.get(a.name);
        const bPerf = this.performanceMetrics.get(b.name);
        
        const aScore = (a.successCount / Math.max(1, a.successCount + a.errorCount)) / 
                      Math.max(1, a.avgResponseTime) * 
                      (1 - (aPerf?.errorRate || 0));
        const bScore = (b.successCount / Math.max(1, b.successCount + b.errorCount)) / 
                      Math.max(1, b.avgResponseTime) * 
                      (1 - (bPerf?.errorRate || 0));
        
        return bScore - aScore;
      });

    // Check rate limits
    for (const provider of availableProviders) {
      const requestCount = this.requestCounts.get(provider.name);
      if (requestCount && requestCount.count < provider.maxRequestsPerMinute) {
        return provider;
      }
    }

    return availableProviders[0] || null;
  }

  private updateProviderMetrics(provider: AIProvider, success: boolean, responseTime: number, tokens: number, cost: number): void {
    const metrics = this.providerMetrics.get(provider.name);
    if (!metrics) return;

    metrics.totalRequests += 1;
    if (success) {
      metrics.successfulRequests += 1;
      provider.successCount += 1;
      this.recordCircuitBreakerSuccess(provider.name);
    } else {
      metrics.failedRequests += 1;
      provider.errorCount += 1;
      this.recordCircuitBreakerFailure(provider.name);
    }
    
    metrics.avgResponseTime = (metrics.avgResponseTime * (metrics.totalRequests - 1) + responseTime) / metrics.totalRequests;
    metrics.totalTokens += tokens;
    metrics.totalCost += cost;
    metrics.lastUpdated = new Date();

    provider.avgResponseTime = metrics.avgResponseTime;

    // Update performance metrics
    this.updatePerformanceMetrics(provider.name, responseTime, success);

    // Update request count for rate limiting
    const requestCount = this.requestCounts.get(provider.name);
    if (requestCount) {
      if (requestCount.resetTime < new Date()) {
        requestCount.count = 1;
        requestCount.resetTime = new Date(Date.now() + 60000);
      } else {
        requestCount.count += 1;
      }
    }
  }

  private logProviderEvent(provider: string, event: string, metadata: any): void {
    if (this.config.enableMetrics) {
      console.log(`[AI Provider ${provider}] ${event}:`, metadata);
      
      // Store in database for analytics
      prisma.analytics.create({
        data: {
          userId: 'system',
          action: `ai_provider_${event}`,
          metadata: {
            provider,
            ...metadata,
            timestamp: new Date().toISOString()
          }
        }
      }).catch(error => {
        console.error('Failed to log provider event:', error);
      });
    }
  }

  private logEvent(event: string, metadata: any): void {
    if (this.config.enableMetrics) {
      console.log(`[AI Service] ${event}:`, metadata);
    }
  }

  onEvent(listener: (event: AgentEvent) => void): void {
    this.eventListeners.push(listener);
  }

  private emitEvent(event: AgentEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in AI event listener:', error);
      }
    });
  }

  async streamCompletion(
    prompt: string,
    messageId: string,
    options: {
      provider?: string;
      model?: string;
      maxTokens?: number;
      temperature?: number;
      userId?: string;
      isHealthCheck?: boolean;
    } = {}
  ): Promise<void> {
    const startTime = Date.now();
    const estimatedTokens = Math.ceil(prompt.length / 4) + (options.maxTokens || this.config.maxTokens);
    
    // Check usage quota
    if (options.userId && !await this.checkUsageQuota(options.userId, estimatedTokens)) {
      this.emitEvent({
        type: 'RunError',
        message: 'Daily usage quota exceeded',
        code: 'QUOTA_EXCEEDED',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Check cache first
    const cacheKey = this.generateCacheKey(prompt, options);
    const cachedResponse = this.getCachedResponse(cacheKey);
    if (cachedResponse && !options.isHealthCheck) {
      this.emitEvent({
        type: 'TextMessageStart',
        messageId: messageId,
        role: 'assistant',
        timestamp: new Date().toISOString()
      });

      // Stream cached response
      for (let i = 0; i < cachedResponse.length; i += 10) {
        const chunk = cachedResponse.substring(i, i + 10);
        this.emitEvent({
          type: 'TextMessageContent',
          messageId: messageId,
          delta: chunk,
          timestamp: new Date().toISOString()
        });
        await new Promise(resolve => setTimeout(resolve, 10)); // Simulate streaming
      }

      this.emitEvent({
        type: 'TextMessageEnd',
        messageId: messageId,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Select optimal provider
    const providerConfig = options.provider 
      ? this.config.providers.find(p => p.name === options.provider)
      : this.selectOptimalProvider();

    if (!providerConfig) {
      this.emitEvent({
        type: 'RunError',
        message: 'No healthy AI providers available',
        code: 'NO_PROVIDERS_AVAILABLE',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Start streaming
    if (!options.isHealthCheck) {
      this.emitEvent({
        type: 'TextMessageStart',
        messageId: messageId,
        role: 'assistant',
        timestamp: new Date().toISOString()
      });
    }

    let fullResponse = '';
    let success = false;
    let tokenCount = 0;

    try {
      const response = await this.makeProviderRequest(providerConfig, prompt, messageId, options);
      fullResponse = response.content;
      tokenCount = response.tokens || estimatedTokens;
      success = true;

      // Cache successful response
      if (!options.isHealthCheck) {
        this.setCachedResponse(cacheKey, fullResponse, providerConfig.name, options.model || providerConfig.model);
      }

      // End streaming
      if (!options.isHealthCheck) {
        this.emitEvent({
          type: 'TextMessageEnd',
          messageId: messageId,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error(`Error with ${providerConfig.name}:`, error);
      
      if (!options.isHealthCheck) {
        // Try fallback provider
        await this.tryFallbackProvider(prompt, messageId, providerConfig.name, options);
      } else {
        throw error;
      }
    } finally {
      // Update metrics
      const responseTime = Date.now() - startTime;
      const cost = (tokenCount / 1000) * providerConfig.costPer1kTokens;
      
      this.updateProviderMetrics(providerConfig, success, responseTime, tokenCount, cost);
      
      // Track usage
      if (options.userId && success && !options.isHealthCheck) {
        await this.trackUsage(options.userId, providerConfig.name, options.model || providerConfig.model, tokenCount, cost, 'completion');
      }
    }
  }

  private async makeProviderRequest(
    provider: AIProvider,
    prompt: string,
    messageId: string,
    options: any
  ): Promise<{ content: string; tokens?: number }> {
    switch (provider.name) {
      case 'openai':
        return await this.streamOpenAI(prompt, messageId, provider, options);
      case 'anthropic':
        return await this.streamAnthropic(prompt, messageId, provider, options);
      case 'groq':
        return await this.streamGroq(prompt, messageId, provider, options);
      case 'openrouter':
        return await this.streamOpenRouter(prompt, messageId, provider, options);
      default:
        throw new Error(`Unsupported provider: ${provider.name}`);
    }
  }

  private async trackUsage(
    userId: string,
    provider: string,
    model: string,
    tokens: number,
    cost: number,
    requestType: string
  ): Promise<void> {
    try {
      await prisma.aIUsage.create({
        data: {
          userId,
          provider: provider as any,
          model,
          tokens,
          cost,
          requestType: requestType as any,
          metadata: {
            timestamp: new Date().toISOString(),
            success: true
          }
        }
      });

      // Update usage quota
      const quota = this.usageQuotas.get(userId) || {
        userId,
        dailyTokens: 0,
        dailyCost: 0,
        monthlyTokens: 0,
        monthlyCost: 0,
        lastReset: new Date()
      };

      quota.dailyTokens += tokens;
      quota.dailyCost += cost;
      quota.monthlyTokens += tokens;
      quota.monthlyCost += cost;
      
      this.usageQuotas.set(userId, quota);
    } catch (error) {
      console.error('Failed to track usage:', error);
    }
  }

  private async tryFallbackProvider(
    prompt: string,
    messageId: string,
    failedProvider: string,
    options: any
  ): Promise<void> {
    const availableProviders = this.config.providers
      .filter(p => p.name !== failedProvider && p.apiKey)
      .map(p => p.name);

    if (availableProviders.length === 0) {
      this.emitEvent({
        type: 'RunError',
        message: 'All AI providers failed',
        code: 'ALL_PROVIDERS_FAILED',
        timestamp: new Date().toISOString()
      });
      return;
    }

    console.log(`Trying fallback provider: ${availableProviders[0]}`);
    
    try {
      await this.streamCompletion(prompt, messageId, {
        ...options,
        provider: availableProviders[0]
      });
    } catch (error) {
      console.error('Fallback provider also failed:', error);
      this.emitEvent({
        type: 'RunError',
        message: `All providers failed. Last error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        code: 'FALLBACK_FAILED',
        timestamp: new Date().toISOString()
      });
    }
  }

  private async streamOpenAI(
    prompt: string,
    messageId: string,
    provider: AIProvider,
    options: any
  ): Promise<{ content: string; tokens?: number }> {
    try {
      // Dynamic import to handle missing package
      const OpenAI = await import('openai').then(m => m.default).catch(() => null);
      
      if (!OpenAI) {
        throw new Error('OpenAI package not installed');
      }

      const client = new OpenAI({
        apiKey: provider.apiKey,
        baseURL: provider.baseUrl,
        timeout: this.config.timeout
      });

      let fullContent = '';
      let tokenCount = 0;

      if (options.isHealthCheck) {
        // Non-streaming for health checks
        const response = await client.chat.completions.create({
          model: options.model || provider.model || 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: options.maxTokens || 5,
          temperature: options.temperature || 0,
          stream: false
        });
        
        return {
          content: response.choices[0]?.message?.content || '',
          tokens: response.usage?.total_tokens
        };
      }

      const stream = await client.chat.completions.create({
        model: options.model || provider.model || 'gpt-4-turbo-preview',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: Math.min(options.maxTokens || this.config.maxTokens, provider.maxTokensPerRequest),
        temperature: options.temperature || this.config.temperature,
        stream: true
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          fullContent += content;
          this.emitEvent({
            type: 'TextMessageContent',
            messageId,
            delta: content,
            timestamp: new Date().toISOString()
          });
        }
        
        // Estimate token count
        if (chunk.usage?.total_tokens) {
          tokenCount = chunk.usage.total_tokens;
        }
      }

      return {
        content: fullContent,
        tokens: tokenCount || Math.ceil(fullContent.length / 4)
      };
    } catch (error) {
      this.logProviderEvent(provider.name, 'request_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        prompt: prompt.substring(0, 100)
      });
      throw error;
    }
  }

  private async streamAnthropic(
    prompt: string,
    messageId: string,
    provider: AIProvider,
    options: any
  ): Promise<{ content: string; tokens?: number }> {
    try {
      // Dynamic import to handle missing package
      const Anthropic = await import('@anthropic-ai/sdk').then(m => m.default).catch(() => null);
      
      if (!Anthropic) {
        throw new Error('Anthropic package not installed');
      }

      const client = new Anthropic({
        apiKey: provider.apiKey,
        timeout: this.config.timeout
      });

      let fullContent = '';
      let tokenCount = 0;

      if (options.isHealthCheck) {
        // Non-streaming for health checks
        const response = await client.messages.create({
          model: options.model || provider.model || 'claude-3-sonnet-20240229',
          max_tokens: options.maxTokens || 5,
          temperature: options.temperature || 0,
          messages: [{ role: 'user', content: prompt }],
          stream: false
        });
        
        return {
          content: response.content[0]?.type === 'text' ? response.content[0].text : '',
          tokens: response.usage?.input_tokens + response.usage?.output_tokens
        };
      }

      const stream = await client.messages.create({
        model: options.model || provider.model || 'claude-3-sonnet-20240229',
        max_tokens: Math.min(options.maxTokens || this.config.maxTokens, provider.maxTokensPerRequest),
        temperature: options.temperature || this.config.temperature,
        messages: [{ role: 'user', content: prompt }],
        stream: true
      });

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          fullContent += chunk.delta.text;
          this.emitEvent({
            type: 'TextMessageContent',
            messageId,
            delta: chunk.delta.text,
            timestamp: new Date().toISOString()
          });
        } else if (chunk.type === 'message_delta' && chunk.usage) {
          tokenCount = chunk.usage.input_tokens + chunk.usage.output_tokens;
        }
      }

      return {
        content: fullContent,
        tokens: tokenCount || Math.ceil(fullContent.length / 4)
      };
    } catch (error) {
      this.logProviderEvent(provider.name, 'request_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        prompt: prompt.substring(0, 100)
      });
      throw error;
    }
  }

  private async streamGroq(
    prompt: string,
    messageId: string,
    provider: AIProvider,
    options: any
  ): Promise<{ content: string; tokens?: number }> {
    try {
      // Dynamic import to handle missing package
      const Groq = await import('groq-sdk').then(m => m.default).catch(() => null);
      
      if (!Groq) {
        throw new Error('Groq package not installed');
      }

      const client = new Groq({
        apiKey: provider.apiKey,
        timeout: this.config.timeout
      });

      let fullContent = '';
      let tokenCount = 0;

      if (options.isHealthCheck) {
        // Non-streaming for health checks
        const response = await client.chat.completions.create({
          model: options.model || provider.model || 'mixtral-8x7b-32768',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: options.maxTokens || 5,
          temperature: options.temperature || 0,
          stream: false
        });
        
        return {
          content: response.choices[0]?.message?.content || '',
          tokens: response.usage?.total_tokens
        };
      }

      const stream = await client.chat.completions.create({
        model: options.model || provider.model || 'mixtral-8x7b-32768',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: Math.min(options.maxTokens || this.config.maxTokens, provider.maxTokensPerRequest),
        temperature: options.temperature || this.config.temperature,
        stream: true
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          fullContent += content;
          this.emitEvent({
            type: 'TextMessageContent',
            messageId,
            delta: content,
            timestamp: new Date().toISOString()
          });
        }
        
        if (chunk.usage?.total_tokens) {
          tokenCount = chunk.usage.total_tokens;
        }
      }

      return {
        content: fullContent,
        tokens: tokenCount || Math.ceil(fullContent.length / 4)
      };
    } catch (error) {
      this.logProviderEvent(provider.name, 'request_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        prompt: prompt.substring(0, 100)
      });
      throw error;
    }
  }

  private async streamOpenRouter(
    prompt: string,
    messageId: string,
    provider: AIProvider,
    options: any
  ): Promise<{ content: string; tokens?: number }> {
    try {
      // Dynamic import to handle missing package
      const axios = await import('axios').then(m => m.default).catch(() => null);
      
      if (!axios) {
        throw new Error('Axios package not installed');
      }

      let fullContent = '';
      let tokenCount = 0;

      if (options.isHealthCheck) {
        // Non-streaming for health checks
        const response = await axios.post(
          provider.baseUrl || 'https://openrouter.ai/api/v1/chat/completions',
          {
            model: options.model || provider.model || 'openai/gpt-4-turbo-preview',
            messages: [{ role: 'user', content: prompt }],
            max_tokens: options.maxTokens || 5,
            temperature: options.temperature || 0,
            user_id: options.userId,
            stream: false
          },
          {
            headers: {
              'Authorization': `Bearer ${provider.apiKey}`,
              'Content-Type': 'application/json',
              'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
              'X-Title': 'TutorAI'
            },
            timeout: this.config.timeout
          }
        );
        
        return {
          content: response.data.choices[0]?.message?.content || '',
          tokens: response.data.usage?.total_tokens
        };
      }

      const response = await axios.post(
        provider.baseUrl || 'https://openrouter.ai/api/v1/chat/completions',
        {
          model: options.model || provider.model || 'openai/gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: Math.min(options.maxTokens || this.config.maxTokens, provider.maxTokensPerRequest),
          temperature: options.temperature || this.config.temperature,
          user_id: options.userId,
          stream: true
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
            'X-Title': 'TutorAI'
          },
          responseType: 'stream',
          timeout: this.config.timeout
        }
      );

      // Parse SSE stream
      let buffer = '';
      
      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk: Buffer) => {
          buffer += chunk.toString();
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                resolve({ content: fullContent, tokens: tokenCount || Math.ceil(fullContent.length / 4) });
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;
                if (content) {
                  fullContent += content;
                  this.emitEvent({
                    type: 'TextMessageContent',
                    messageId,
                    delta: content,
                    timestamp: new Date().toISOString()
                  });
                }
                
                if (parsed.usage?.total_tokens) {
                  tokenCount = parsed.usage.total_tokens;
                }
              } catch (e) {
                // Ignore parsing errors for malformed chunks
              }
            }
          }
        });

        response.data.on('end', () => {
          resolve({ content: fullContent, tokens: tokenCount || Math.ceil(fullContent.length / 4) });
        });
        
        response.data.on('error', reject);
      });

    } catch (error) {
      this.logProviderEvent(provider.name, 'request_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        prompt: prompt.substring(0, 100)
      });
      throw error;
    }
  }

  async explainElement(
    selector: string,
    question: string | undefined,
    pageContext: string,
    runId: string,
    threadId: string,
    userId?: string
  ): Promise<void> {
    this.emitEvent({
      type: 'RunStarted',
      runId,
      threadId,
      timestamp: new Date().toISOString()
    });

    try {
      const messageId = `msg_${Date.now()}`;
      const prompt = question 
        ? `User is asking about element "${selector}" on page ${pageContext}: "${question}". Please provide a helpful explanation.`
        : `Please explain what the element "${selector}" does on page ${pageContext} and how the user can interact with it.`;

      await this.streamCompletion(prompt, messageId, { userId });

      // Emit tool call for highlighting
      await this.emitHighlightTool(selector, runId, threadId);

      this.emitEvent({
        type: 'RunFinished',
        runId,
        threadId,
        result: { success: true },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error explaining element:', error);
      this.emitEvent({
        type: 'RunError',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'ELEMENT_EXPLANATION_ERROR',
        timestamp: new Date().toISOString()
      });
    }
  }

  private async emitHighlightTool(selector: string, runId: string, threadId: string): Promise<void> {
    const toolCallId = `tool_${Date.now()}`;

    this.emitEvent({
      type: 'ToolCallStart',
      toolCallId,
      toolCallName: 'highlight_element',
      timestamp: new Date().toISOString()
    });

    const args = JSON.stringify({ selector, style: { borderColor: '#3b82f6', pulseAnimation: true } });
    this.emitEvent({
      type: 'ToolCallArgs',
      toolCallId,
      delta: args,
      timestamp: new Date().toISOString()
    });

    this.emitEvent({
      type: 'ToolCallEnd',
      toolCallId,
      timestamp: new Date().toISOString()
    });

    this.emitEvent({
      type: 'ToolCallResult',
      messageId: `msg_${Date.now()}`,
      toolCallId,
      content: `Element ${selector} highlighted successfully`,
      role: 'tool',
      timestamp: new Date().toISOString()
    });
  }


  // Voice command recognition implementation
  async processVoiceCommand(audioBlob: Blob, userId?: string): Promise<{ command: string; confidence: number; action?: string }> {
    try {
      // Convert audio to text using OpenAI Whisper or similar
      const transcription = await this.transcribeAudio(audioBlob, userId);
      // Process command using NLP
      const command = await this.parseVoiceCommand(transcription, userId);
      return command;
    } catch (error) {
      console.error('Voice command processing failed:', error);
      throw error;
    }
  }

  private async transcribeAudio(audioBlob: Blob, userId?: string): Promise<string> {
    const provider = this.selectOptimalProvider();
    if (!provider || provider.name !== 'openai') {
      throw new Error('Voice transcription requires OpenAI provider');
    }

    try {
      const OpenAI = await import('openai').then(m => m.default).catch(() => null);
      if (!OpenAI) {
        throw new Error('OpenAI package not available for transcription');
      }

      const client = new OpenAI({
        apiKey: provider.apiKey,
        timeout: this.config.timeout
      });

      const formData = new FormData();
      formData.append('file', audioBlob, 'audio.webm');
      formData.append('model', 'whisper-1');
      formData.append('language', 'en');

      const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${provider.apiKey}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Transcription failed: ${response.status}`);
      }

      const result = await response.json();
      
      // Track usage
      if (userId) {
        await this.trackUsage(userId, 'openai', 'whisper-1', 100, 0.006, 'transcription');
      }

      return result.text || '';
    } catch (error) {
      this.logProviderEvent(provider.name, 'transcription_failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async parseVoiceCommand(text: string, userId?: string): Promise<{ command: string; confidence: number; action?: string }> {
    const normalizedText = text.toLowerCase().trim();
    
    // Define command patterns
    const commandPatterns = [
      { pattern: /explain (this|that|the .+)/, action: 'explain_element', confidence: 0.9 },
      { pattern: /what (is|does) (this|that|the .+)/, action: 'explain_element', confidence: 0.8 },
      { pattern: /help me with (this|that|the .+)/, action: 'explain_element', confidence: 0.7 },
      { pattern: /start tutorial/, action: 'start_tutorial', confidence: 0.9 },
      { pattern: /begin tutorial/, action: 'start_tutorial', confidence: 0.9 },
      { pattern: /next step/, action: 'next_step', confidence: 0.9 },
      { pattern: /previous step/, action: 'previous_step', confidence: 0.9 },
      { pattern: /go back/, action: 'previous_step', confidence: 0.8 },
      { pattern: /stop tutorial/, action: 'stop_tutorial', confidence: 0.9 },
      { pattern: /pause tutorial/, action: 'pause_tutorial', confidence: 0.9 },
      { pattern: /resume tutorial/, action: 'resume_tutorial', confidence: 0.9 },
      { pattern: /repeat/, action: 'repeat_step', confidence: 0.8 },
      { pattern: /say that again/, action: 'repeat_step', confidence: 0.8 }
    ];

    // Find best matching pattern
    let bestMatch = { command: normalizedText, confidence: 0.1, action: 'unknown' };
    
    for (const pattern of commandPatterns) {
      if (pattern.pattern.test(normalizedText)) {
        if (pattern.confidence > bestMatch.confidence) {
          bestMatch = {
            command: normalizedText,
            confidence: pattern.confidence,
            action: pattern.action
          };
        }
      }
    }

    // Use AI for complex command parsing if confidence is low
    if (bestMatch.confidence < 0.6) {
      try {
        const aiParsed = await this.parseCommandWithAI(normalizedText, userId);
        if (aiParsed.confidence > bestMatch.confidence) {
          bestMatch = aiParsed;
        }
      } catch (error) {
        console.warn('AI command parsing failed:', error);
      }
    }

    return bestMatch;
  }

  private async parseCommandWithAI(text: string, userId?: string): Promise<{ command: string; confidence: number; action?: string }> {
    const prompt = `Parse this voice command and determine the user's intent:

Command: "${text}"

Possible actions:
- explain_element: User wants explanation of a UI element
- start_tutorial: User wants to start a tutorial
- next_step: User wants to go to next tutorial step
- previous_step: User wants to go back
- stop_tutorial: User wants to stop the tutorial
- pause_tutorial: User wants to pause
- resume_tutorial: User wants to resume
- repeat_step: User wants to repeat current step
- unknown: Command not recognized

Respond with JSON: {"action": "action_name", "confidence": 0.0-1.0, "parameters": {}}`;

    try {
      const messageId = `cmd_${Date.now()}`;
      let response = '';
      
      const eventListener = (event: AgentEvent) => {
        if (event.type === 'TextMessageContent') {
          const contentEvent = event as any;
          response += contentEvent.delta;
        }
      };

      this.onEvent(eventListener);
      
      await this.streamCompletion(prompt, messageId, { 
        userId, 
        maxTokens: 200, 
        temperature: 0.1 
      });

      // Parse AI response
      const parsed = JSON.parse(response.trim());
      return {
        command: text,
        confidence: parsed.confidence || 0.5,
        action: parsed.action || 'unknown'
      };
    } catch (error) {
      console.error('AI command parsing error:', error);
      return { command: text, confidence: 0.1, action: 'unknown' };
    }
  }

  // Enhanced error recovery and circuit breaker pattern
  private circuitBreakers: Map<string, {
    failures: number;
    lastFailure: Date;
    state: 'closed' | 'open' | 'half-open';
    nextAttempt: Date;
  }> = new Map();

  private checkCircuitBreaker(providerName: string): boolean {
    const breaker = this.circuitBreakers.get(providerName);
    if (!breaker) {
      this.circuitBreakers.set(providerName, {
        failures: 0,
        lastFailure: new Date(),
        state: 'closed',
        nextAttempt: new Date()
      });
      return true;
    }

    const now = new Date();
    
    switch (breaker.state) {
      case 'closed':
        return true;
      
      case 'open':
        if (now >= breaker.nextAttempt) {
          breaker.state = 'half-open';
          return true;
        }
        return false;
      
      case 'half-open':
        return true;
      
      default:
        return true;
    }
  }

  private recordCircuitBreakerSuccess(providerName: string): void {
    const breaker = this.circuitBreakers.get(providerName);
    if (breaker) {
      breaker.failures = 0;
      breaker.state = 'closed';
    }
  }

  private recordCircuitBreakerFailure(providerName: string): void {
    const breaker = this.circuitBreakers.get(providerName);
    if (breaker) {
      breaker.failures += 1;
      breaker.lastFailure = new Date();
      
      if (breaker.failures >= 5) {
        breaker.state = 'open';
        breaker.nextAttempt = new Date(Date.now() + 60000); // 1 minute
      }
    }
  }

  // Advanced caching with compression and TTL
  private compressResponse(response: string): string {
    // Simple compression - in production use a proper compression library
    return response.length > 1000 ? response.substring(0, 1000) + '...' : response;
  }

  private decompressResponse(compressed: string): string {
    // Decompression logic would go here
    return compressed;
  }

  // Performance monitoring and alerting
  private performanceMetrics: Map<string, {
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    requestCount: number;
    errorRate: number;
    lastUpdated: Date;
  }> = new Map();

  private updatePerformanceMetrics(providerName: string, responseTime: number, success: boolean): void {
    let metrics = this.performanceMetrics.get(providerName);
    if (!metrics) {
      metrics = {
        averageResponseTime: responseTime,
        p95ResponseTime: responseTime,
        p99ResponseTime: responseTime,
        requestCount: 1,
        errorRate: success ? 0 : 1,
        lastUpdated: new Date()
      };
    } else {
      metrics.requestCount += 1;
      metrics.averageResponseTime = (metrics.averageResponseTime * (metrics.requestCount - 1) + responseTime) / metrics.requestCount;
      metrics.errorRate = success ? metrics.errorRate * 0.99 : Math.min(1, metrics.errorRate + 0.01);
      metrics.lastUpdated = new Date();
    }
    
    this.performanceMetrics.set(providerName, metrics);
    
    // Alert if performance degrades
    if (metrics.errorRate > 0.1 || metrics.averageResponseTime > 10000) {
      this.logProviderEvent(providerName, 'performance_alert', {
        errorRate: metrics.errorRate,
        averageResponseTime: metrics.averageResponseTime,
        requestCount: metrics.requestCount
      });
    }
  }

  // Public methods for metrics and management
  getProviderMetrics(): ProviderMetrics[] {
    return Array.from(this.providerMetrics.values());
  }

  getUsageQuotas(): UsageQuota[] {
    return Array.from(this.usageQuotas.values());
  }

  getCacheStats(): { size: number; hitRate: number } {
    const totalRequests = Array.from(this.providerMetrics.values())
      .reduce((sum, metrics) => sum + metrics.totalRequests, 0);
    const cacheHits = this.responseCache.size; // Simplified metric
    
    return {
      size: this.responseCache.size,
      hitRate: totalRequests > 0 ? cacheHits / totalRequests : 0
    };
  }

  getPerformanceMetrics(): Map<string, any> {
    return this.performanceMetrics;
  }

  getCircuitBreakerStatus(): Map<string, any> {
    return this.circuitBreakers;
  }

  async resetProviderHealth(providerName: string): Promise<void> {
    const provider = this.config.providers.find(p => p.name === providerName);
    if (provider) {
      provider.isHealthy = true;
      provider.errorCount = 0;
      provider.successCount = 0;
      this.recordCircuitBreakerSuccess(providerName);
      await this.checkProviderHealth(provider);
    }
  }

  clearCache(): void {
    this.responseCache.clear();
    this.logEvent('cache_cleared', { timestamp: new Date().toISOString() });
  }

  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    this.responseCache.clear();
    this.usageQuotas.clear();
    this.providerMetrics.clear();
    this.requestCounts.clear();
    this.circuitBreakers.clear();
    this.performanceMetrics.clear();
    this.eventListeners = [];
  }
}

// Default configuration
export const defaultAIConfig: AIProviderConfig = {
  providers: [
    {
      name: 'openai',
      apiKey: process.env.OPENAI_API_KEY || '',
      model: 'gpt-4-turbo-preview',
      priority: 10,
      maxRequestsPerMinute: 60,
      maxTokensPerRequest: 4000,
      costPer1kTokens: 0.01,
      isHealthy: true,
      lastHealthCheck: new Date(),
      errorCount: 0,
      successCount: 0,
      avgResponseTime: 0
    },
    {
      name: 'anthropic',
      apiKey: process.env.ANTHROPIC_API_KEY || '',
      model: 'claude-3-sonnet-20240229',
      priority: 9,
      maxRequestsPerMinute: 50,
      maxTokensPerRequest: 4000,
      costPer1kTokens: 0.015,
      isHealthy: true,
      lastHealthCheck: new Date(),
      errorCount: 0,
      successCount: 0,
      avgResponseTime: 0
    },
    {
      name: 'groq',
      apiKey: process.env.GROQ_API_KEY || '',
      model: 'mixtral-8x7b-32768',
      priority: 8,
      maxRequestsPerMinute: 100,
      maxTokensPerRequest: 8000,
      costPer1kTokens: 0.0005,
      isHealthy: true,
      lastHealthCheck: new Date(),
      errorCount: 0,
      successCount: 0,
      avgResponseTime: 0
    },
    {
      name: 'openrouter',
      apiKey: process.env.OPENROUTER_API_KEY || '',
      model: 'openai/gpt-4-turbo-preview',
      baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
      priority: 7,
      maxRequestsPerMinute: 40,
      maxTokensPerRequest: 4000,
      costPer1kTokens: 0.012,
      isHealthy: true,
      lastHealthCheck: new Date(),
      errorCount: 0,
      successCount: 0,
      avgResponseTime: 0
    }
  ].filter(provider => provider.apiKey), // Only include providers with API keys
  defaultProvider: 'openai',
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  enableCaching: true,
  cacheExpiryMinutes: 60,
  enableLoadBalancing: true,
  enableUsageQuotas: true,
  maxDailyUsagePerUser: 100000, // 100k tokens per day
  enableMetrics: true
};

// Singleton instance
let aiService: MultiProviderAIService | null = null;

export function getAIService(): MultiProviderAIService {
  if (!aiService) {
    aiService = new MultiProviderAIService(defaultAIConfig);
  }
  return aiService;
}
