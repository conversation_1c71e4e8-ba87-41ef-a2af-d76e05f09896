"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { Menu, X, Sparkles } from "lucide-react";
import {
  useFocusTrap,
  useId,
  useKeyboardNavigation,
  useAnnouncer
} from "@/hooks/useAccessibility";
import { KeyboardKeys } from "@/lib/accessibility";

interface NavigationItem {
  href: string;
  label: string;
  external?: boolean;
}

interface ResponsiveNavigationProps {
  items: NavigationItem[];
  logo?: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
}

export function ResponsiveNavigation({
  items,
  logo,
  actions,
  className,
}: ResponsiveNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const navId = useId('main-navigation');
  const menuButtonId = useId('menu-button');
  const { announce } = useAnnouncer();
  const focusTrapRef = useFocusTrap(isOpen);

  // Close mobile menu when route changes
  useEffect(() => {
    if (isOpen) {
      setIsOpen(false);
      announce("Navigation menu closed");
    }
  }, [pathname, isOpen, announce]);

  // Handle keyboard navigation
  const handleKeyDown = useKeyboardNavigation({
    ESCAPE: () => {
      if (isOpen) {
        setIsOpen(false);
        announce("Navigation menu closed");
      }
    },
  }, [isOpen, announce]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isOpen, handleKeyDown]);

  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  const defaultLogo = (
    <Link href="/" className="flex items-center gap-2" aria-label="TutorAI Home">
      <Sparkles className="h-6 w-6 text-primary" aria-hidden="true" />
      <span className="text-xl font-bold">TutorAI</span>
    </Link>
  );

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        className
      )}
      role="banner"
    >
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <div className="flex items-center">
          {logo || defaultLogo}
        </div>

        {/* Desktop Navigation */}
        <nav
          className="hidden md:flex items-center gap-6"
          role="navigation"
          aria-label="Main navigation"
          id={navId}
        >
          {items.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-sm px-2 py-1",
                isActive(item.href)
                  ? "text-primary"
                  : "text-muted-foreground"
              )}
              aria-current={isActive(item.href) ? "page" : undefined}
              {...(item.external && {
                target: "_blank",
                rel: "noopener noreferrer",
                "aria-label": `${item.label} (opens in new tab)`,
              })}
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Desktop Actions */}
        <div className="hidden md:flex items-center gap-4">
          {actions}
        </div>

        {/* Mobile Menu */}
        <div className="flex md:hidden items-center gap-2">
          {/* Mobile Actions (limited) */}
          <div className="flex items-center gap-2">
            {actions}
          </div>

          {/* Mobile Menu Trigger */}
          <Sheet open={isOpen} onOpenChange={(open) => {
            setIsOpen(open);
            announce(open ? "Navigation menu opened" : "Navigation menu closed");
          }}>
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden"
                aria-label={isOpen ? "Close navigation menu" : "Open navigation menu"}
                aria-expanded={isOpen}
                aria-controls={navId}
                id={menuButtonId}
              >
                <Menu className="h-5 w-5" aria-hidden="true" />
                <span className="sr-only">
                  {isOpen ? "Close" : "Open"} navigation menu
                </span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-[300px] sm:w-[400px]"
              ref={focusTrapRef}
              aria-labelledby={menuButtonId}
            >
              <div className="flex flex-col h-full">
                {/* Mobile Header */}
                <div className="flex items-center justify-between pb-4 border-b">
                  {logo || defaultLogo}
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setIsOpen(false)}
                    aria-label="Close navigation menu"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                {/* Mobile Navigation */}
                <nav className="flex flex-col gap-2 py-6 flex-1">
                  {items.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        isActive(item.href)
                          ? "bg-primary/10 text-primary"
                          : "text-muted-foreground hover:text-primary hover:bg-accent"
                      )}
                      onClick={() => setIsOpen(false)}
                      {...(item.external && {
                        target: "_blank",
                        rel: "noopener noreferrer",
                      })}
                    >
                      {item.label}
                    </Link>
                  ))}
                </nav>

                {/* Mobile Footer Actions */}
                <div className="border-t pt-4 space-y-2">
                  {actions}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}

// Default navigation items for the main site
export const defaultNavigationItems: NavigationItem[] = [
  { href: "#features", label: "Features" },
  { href: "#demo", label: "Demo" },
  { href: "#dashboard", label: "Dashboard" },
  { href: "#pricing", label: "Pricing" },
];

// Dashboard navigation items
export const dashboardNavigationItems: NavigationItem[] = [
  { href: "/dashboard", label: "Overview" },
  { href: "/tutorials", label: "Tutorials" },
  { href: "/analytics", label: "Analytics" },
  { href: "/settings", label: "Settings" },
];
