import { useState, useCallback, useRef, useEffect } from 'react';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  success: boolean;
}

export interface UseAsyncStateOptions {
  initialData?: any;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  resetOnNewRequest?: boolean;
}

export function useAsyncState<T = any>(options: UseAsyncStateOptions = {}) {
  const {
    initialData = null,
    onSuccess,
    onError,
    resetOnNewRequest = true
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    success: false,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const execute = useCallback(async (asyncFunction: () => Promise<T>) => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    if (resetOnNewRequest) {
      setState({
        data: initialData,
        loading: true,
        error: null,
        success: false,
      });
    } else {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        success: false,
      }));
    }

    try {
      const result = await asyncFunction();
      
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      setState({
        data: result,
        loading: false,
        error: null,
        success: true,
      });

      onSuccess?.(result);
      return result;
    } catch (error) {
      // Check if request was aborted
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      
      setState({
        data: initialData,
        loading: false,
        error: errorMessage,
        success: false,
      });

      onError?.(errorMessage);
      throw error;
    }
  }, [initialData, onSuccess, onError, resetOnNewRequest]);

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      data: initialData,
      loading: false,
      error: null,
      success: false,
    });
  }, [initialData]);

  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data,
      success: true,
    }));
  }, []);

  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      error,
      loading: false,
      success: false,
    }));
  }, []);

  return {
    ...state,
    execute,
    reset,
    setData,
    setError,
    isIdle: !state.loading && !state.error && !state.success,
  };
}

// Specialized hook for API calls
export function useApiCall<T = any>(options: UseAsyncStateOptions = {}) {
  const asyncState = useAsyncState<T>(options);

  const call = useCallback(async (
    url: string, 
    options: RequestInit = {}
  ) => {
    return asyncState.execute(async () => {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    });
  }, [asyncState]);

  return {
    ...asyncState,
    call,
  };
}

// Hook for managing multiple async operations
export function useAsyncOperations() {
  const [operations, setOperations] = useState<Record<string, AsyncState<any>>>({});

  const setOperation = useCallback((key: string, state: Partial<AsyncState<any>>) => {
    setOperations(prev => ({
      ...prev,
      [key]: {
        data: null,
        loading: false,
        error: null,
        success: false,
        ...prev[key],
        ...state,
      },
    }));
  }, []);

  const executeOperation = useCallback(async <T>(
    key: string,
    asyncFunction: () => Promise<T>
  ) => {
    setOperation(key, { loading: true, error: null, success: false });

    try {
      const result = await asyncFunction();
      setOperation(key, { 
        data: result, 
        loading: false, 
        success: true 
      });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setOperation(key, { 
        loading: false, 
        error: errorMessage, 
        success: false 
      });
      throw error;
    }
  }, [setOperation]);

  const resetOperation = useCallback((key: string) => {
    setOperation(key, {
      data: null,
      loading: false,
      error: null,
      success: false,
    });
  }, [setOperation]);

  const getOperation = useCallback((key: string): AsyncState<any> => {
    return operations[key] || {
      data: null,
      loading: false,
      error: null,
      success: false,
    };
  }, [operations]);

  const isAnyLoading = Object.values(operations).some(op => op.loading);
  const hasAnyError = Object.values(operations).some(op => op.error);

  return {
    operations,
    setOperation,
    executeOperation,
    resetOperation,
    getOperation,
    isAnyLoading,
    hasAnyError,
  };
}

// Hook for debounced async operations
export function useDebouncedAsync<T = any>(
  delay: number = 300,
  options: UseAsyncStateOptions = {}
) {
  const asyncState = useAsyncState<T>(options);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedExecute = useCallback((asyncFunction: () => Promise<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      asyncState.execute(asyncFunction);
    }, delay);
  }, [asyncState, delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    ...asyncState,
    debouncedExecute,
  };
}
