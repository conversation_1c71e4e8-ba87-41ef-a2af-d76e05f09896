/**
 * TutorAI Error Reporter
 * Comprehensive error tracking, categorization, and reporting system
 */

class TutorAIErrorReporter {
  constructor() {
    this.errors = [];
    this.errorCategories = {
      'network': ['fetch', 'xhr', 'timeout', 'cors'],
      'runtime': ['reference', 'type', 'syntax', 'range'],
      'permission': ['permission', 'access', 'security'],
      'content_script': ['injection', 'communication', 'dom'],
      'ag_ui': ['connection', 'stream', 'protocol'],
      'storage': ['quota', 'sync', 'corruption'],
      'performance': ['memory', 'timeout', 'slow']
    };
    
    this.severityLevels = {
      'critical': 1,
      'high': 2,
      'medium': 3,
      'low': 4,
      'info': 5
    };
    
    this.reportingEnabled = true;
    this.maxErrors = 100; // Keep last 100 errors
    this.reportingInterval = 5 * 60 * 1000; // Report every 5 minutes
    this.batchSize = 10; // Send errors in batches
    
    this.init();
  }

  async init() {
    // Set up global error handlers
    this.setupGlobalErrorHandlers();
    
    // Set up periodic reporting
    this.setupPeriodicReporting();
    
    // Load settings
    await this.loadSettings();
    
    console.log('TutorAI: Error Reporter initialized');
  }

  setupGlobalErrorHandlers() {
    // Global JavaScript errors
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        severity: this.determineSeverity(event.error)
      });
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: 'unhandled_promise_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
        severity: 'high'
      });
    });

    // Chrome extension specific errors
    if (chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'ERROR_REPORT') {
          this.captureError(message.error);
        }
      });
    }
  }

  setupPeriodicReporting() {
    setInterval(() => {
      this.reportPendingErrors();
    }, this.reportingInterval);
  }

  async loadSettings() {
    try {
      if (chrome.storage) {
        const result = await chrome.storage.sync.get(['tutorAISettings']);
        const settings = result.tutorAISettings || {};
        this.reportingEnabled = settings.errorReportingEnabled !== false;
      }
    } catch (error) {
      console.warn('TutorAI: Failed to load error reporting settings:', error);
    }
  }

  captureError(errorData) {
    if (!this.reportingEnabled) return;

    try {
      const error = this.processError(errorData);
      
      // Add to local storage
      this.errors.push(error);
      
      // Keep only recent errors
      if (this.errors.length > this.maxErrors) {
        this.errors = this.errors.slice(-this.maxErrors);
      }
      
      // Immediate reporting for critical errors
      if (error.severity === 'critical') {
        this.reportError(error);
      }
      
      // Show user-friendly message for high severity errors
      if (this.severityLevels[error.severity] <= 2) {
        this.showUserFriendlyError(error);
      }
      
    } catch (processingError) {
      console.error('TutorAI: Failed to process error:', processingError);
    }
  }

  processError(errorData) {
    const timestamp = Date.now();
    const errorId = this.generateErrorId();
    
    const processedError = {
      id: errorId,
      timestamp: timestamp,
      type: errorData.type || 'unknown',
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      filename: errorData.filename,
      lineno: errorData.lineno,
      colno: errorData.colno,
      severity: errorData.severity || this.determineSeverity(errorData),
      category: this.categorizeError(errorData),
      context: this.gatherContext(),
      userAgent: navigator.userAgent,
      url: window.location?.href || 'extension',
      reported: false,
      retryCount: 0
    };
    
    return processedError;
  }

  generateErrorId() {
    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  determineSeverity(error) {
    if (!error) return 'low';
    
    const message = (error.message || '').toLowerCase();
    const stack = (error.stack || '').toLowerCase();
    
    // Critical errors
    if (message.includes('out of memory') || 
        message.includes('quota exceeded') ||
        message.includes('security') ||
        stack.includes('chrome-extension://')) {
      return 'critical';
    }
    
    // High severity errors
    if (message.includes('network') ||
        message.includes('timeout') ||
        message.includes('permission') ||
        message.includes('cors')) {
      return 'high';
    }
    
    // Medium severity errors
    if (message.includes('reference') ||
        message.includes('undefined') ||
        message.includes('null')) {
      return 'medium';
    }
    
    return 'low';
  }

  categorizeError(errorData) {
    const message = (errorData.message || '').toLowerCase();
    const type = (errorData.type || '').toLowerCase();
    const stack = (errorData.stack || '').toLowerCase();
    
    for (const [category, keywords] of Object.entries(this.errorCategories)) {
      for (const keyword of keywords) {
        if (message.includes(keyword) || type.includes(keyword) || stack.includes(keyword)) {
          return category;
        }
      }
    }
    
    return 'unknown';
  }

  gatherContext() {
    const context = {
      timestamp: new Date().toISOString(),
      url: window.location?.href || 'extension',
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
    
    // Add extension-specific context
    if (chrome.runtime) {
      context.extensionId = chrome.runtime.id;
      context.extensionVersion = chrome.runtime.getManifest()?.version;
    }
    
    // Add performance context
    if (performance && performance.memory) {
      context.memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    
    return context;
  }

  showUserFriendlyError(error) {
    const userMessage = this.getUserFriendlyMessage(error);
    const recoveryActions = this.getRecoveryActions(error);
    
    // Send to content script or popup to display
    if (chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'SHOW_USER_ERROR',
        error: {
          message: userMessage,
          actions: recoveryActions,
          severity: error.severity,
          category: error.category
        }
      }).catch(() => {
        // Fallback to console if messaging fails
        console.warn('TutorAI Error:', userMessage);
      });
    }
  }

  getUserFriendlyMessage(error) {
    switch (error.category) {
      case 'network':
        return 'Connection issue detected. Please check your internet connection.';
      case 'permission':
        return 'Permission error. Please refresh the page and try again.';
      case 'content_script':
        return 'Page interaction error. Please reload the page.';
      case 'ag_ui':
        return 'AI service temporarily unavailable. Retrying automatically.';
      case 'storage':
        return 'Storage issue detected. Some settings may not be saved.';
      case 'performance':
        return 'Performance issue detected. Consider closing other tabs.';
      default:
        return 'An unexpected error occurred. The extension will continue working.';
    }
  }

  getRecoveryActions(error) {
    const actions = [];
    
    switch (error.category) {
      case 'network':
        actions.push({ text: 'Retry', action: 'retry' });
        actions.push({ text: 'Check Connection', action: 'check_connection' });
        break;
      case 'permission':
        actions.push({ text: 'Refresh Page', action: 'refresh_page' });
        actions.push({ text: 'Grant Permissions', action: 'grant_permissions' });
        break;
      case 'content_script':
        actions.push({ text: 'Reload Page', action: 'reload_page' });
        break;
      case 'ag_ui':
        actions.push({ text: 'Retry Connection', action: 'retry_connection' });
        break;
      case 'storage':
        actions.push({ text: 'Clear Cache', action: 'clear_cache' });
        break;
      case 'performance':
        actions.push({ text: 'Optimize Settings', action: 'optimize_settings' });
        break;
    }
    
    actions.push({ text: 'Report Issue', action: 'report_issue' });
    return actions;
  }

  async reportError(error) {
    if (!this.reportingEnabled || error.reported) return;
    
    try {
      const settings = await this.getSettings();
      if (!settings.tutorAIServer) return;
      
      const response = await fetch(`${settings.tutorAIServer}/api/analytics/error-report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          extensionId: chrome.runtime?.id || 'unknown',
          error: error,
          timestamp: new Date().toISOString()
        })
      });
      
      if (response.ok) {
        error.reported = true;
        error.reportedAt = Date.now();
      } else {
        error.retryCount++;
        if (error.retryCount < 3) {
          // Retry later
          setTimeout(() => this.reportError(error), 30000 * error.retryCount);
        }
      }
      
    } catch (reportingError) {
      console.error('TutorAI: Failed to report error:', reportingError);
      error.retryCount++;
    }
  }

  async reportPendingErrors() {
    const pendingErrors = this.errors.filter(error => !error.reported);
    
    if (pendingErrors.length === 0) return;
    
    // Report in batches
    for (let i = 0; i < pendingErrors.length; i += this.batchSize) {
      const batch = pendingErrors.slice(i, i + this.batchSize);
      await this.reportErrorBatch(batch);
    }
  }

  async reportErrorBatch(errors) {
    try {
      const settings = await this.getSettings();
      if (!settings.tutorAIServer) return;
      
      const response = await fetch(`${settings.tutorAIServer}/api/analytics/error-batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          extensionId: chrome.runtime?.id || 'unknown',
          errors: errors,
          timestamp: new Date().toISOString()
        })
      });
      
      if (response.ok) {
        errors.forEach(error => {
          error.reported = true;
          error.reportedAt = Date.now();
        });
      }
      
    } catch (error) {
      console.error('TutorAI: Failed to report error batch:', error);
    }
  }

  async getSettings() {
    try {
      if (chrome.storage) {
        const result = await chrome.storage.sync.get(['tutorAISettings']);
        return result.tutorAISettings || {};
      }
    } catch (error) {
      console.warn('TutorAI: Failed to get settings:', error);
    }
    return {};
  }

  // Public API methods
  getErrors() {
    return this.errors;
  }

  getErrorSummary() {
    const summary = {
      total: this.errors.length,
      reported: this.errors.filter(e => e.reported).length,
      pending: this.errors.filter(e => !e.reported).length,
      bySeverity: {},
      byCategory: {},
      recent: this.errors.filter(e => Date.now() - e.timestamp < 3600000) // Last hour
    };
    
    // Group by severity
    Object.keys(this.severityLevels).forEach(severity => {
      summary.bySeverity[severity] = this.errors.filter(e => e.severity === severity).length;
    });
    
    // Group by category
    Object.keys(this.errorCategories).forEach(category => {
      summary.byCategory[category] = this.errors.filter(e => e.category === category).length;
    });
    
    return summary;
  }

  clearErrors() {
    this.errors = [];
  }

  setReportingEnabled(enabled) {
    this.reportingEnabled = enabled;
  }

  // Manual error reporting
  reportCustomError(message, category = 'unknown', severity = 'medium') {
    this.captureError({
      type: 'manual_report',
      message: message,
      category: category,
      severity: severity
    });
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TutorAIErrorReporter;
} else {
  window.TutorAIErrorReporter = TutorAIErrorReporter;
}
