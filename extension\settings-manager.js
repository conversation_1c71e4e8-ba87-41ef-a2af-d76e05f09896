/**
 * TutorAI Settings Manager
 * Handles settings synchronization, backup/restore, validation, and migration
 */

class TutorAISettingsManager {
  constructor() {
    this.defaultSettings = {
      // Core settings
      enabled: true,
      autoStart: false,
      voiceEnabled: false,
      highlightColor: '#3b82f6',
      tutorAIServer: 'http://localhost:3000',
      
      // Advanced settings
      animationSpeed: 'normal', // slow, normal, fast
      tutorialDelay: 2000,
      maxCacheSize: 50, // MB
      debugMode: false,
      
      // Privacy settings
      analyticsEnabled: true,
      errorReportingEnabled: true,
      usageDataSharing: true,
      
      // Accessibility settings
      highContrast: false,
      reducedMotion: false,
      fontSize: 'medium', // small, medium, large
      
      // Notification settings
      updateNotifications: true,
      tutorialNotifications: true,
      errorNotifications: true,
      
      // Performance settings
      preloadContent: true,
      backgroundSync: true,
      offlineMode: false,
      
      // Metadata
      version: '1.0.0',
      lastSync: null,
      deviceId: null,
      migrationVersion: '1.0.0'
    };
    
    this.settingsSchema = this.createSettingsSchema();
    this.syncInProgress = false;
    this.backupInterval = null;
    
    this.init();
  }

  async init() {
    // Generate device ID if not exists
    await this.ensureDeviceId();
    
    // Load and validate settings
    await this.loadSettings();
    
    // Set up automatic backup
    this.setupAutoBackup();
    
    // Set up sync listeners
    this.setupSyncListeners();
    
    console.log('TutorAI: Settings Manager initialized');
  }

  createSettingsSchema() {
    return {
      enabled: { type: 'boolean', default: true },
      autoStart: { type: 'boolean', default: false },
      voiceEnabled: { type: 'boolean', default: false },
      highlightColor: { 
        type: 'string', 
        default: '#3b82f6',
        validate: (value) => /^#[0-9A-F]{6}$/i.test(value)
      },
      tutorAIServer: { 
        type: 'string', 
        default: 'http://localhost:3000',
        validate: (value) => {
          try {
            new URL(value);
            return true;
          } catch {
            return false;
          }
        }
      },
      animationSpeed: { 
        type: 'string', 
        default: 'normal',
        validate: (value) => ['slow', 'normal', 'fast'].includes(value)
      },
      tutorialDelay: { 
        type: 'number', 
        default: 2000,
        validate: (value) => value >= 0 && value <= 10000
      },
      maxCacheSize: { 
        type: 'number', 
        default: 50,
        validate: (value) => value >= 10 && value <= 500
      },
      debugMode: { type: 'boolean', default: false },
      analyticsEnabled: { type: 'boolean', default: true },
      errorReportingEnabled: { type: 'boolean', default: true },
      usageDataSharing: { type: 'boolean', default: true },
      highContrast: { type: 'boolean', default: false },
      reducedMotion: { type: 'boolean', default: false },
      fontSize: { 
        type: 'string', 
        default: 'medium',
        validate: (value) => ['small', 'medium', 'large'].includes(value)
      },
      updateNotifications: { type: 'boolean', default: true },
      tutorialNotifications: { type: 'boolean', default: true },
      errorNotifications: { type: 'boolean', default: true },
      preloadContent: { type: 'boolean', default: true },
      backgroundSync: { type: 'boolean', default: true },
      offlineMode: { type: 'boolean', default: false }
    };
  }

  async ensureDeviceId() {
    const result = await chrome.storage.local.get(['deviceId']);
    if (!result.deviceId) {
      const deviceId = this.generateDeviceId();
      await chrome.storage.local.set({ deviceId });
      this.defaultSettings.deviceId = deviceId;
    } else {
      this.defaultSettings.deviceId = result.deviceId;
    }
  }

  generateDeviceId() {
    return 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.sync.get(['tutorAISettings']);
      let settings = result.tutorAISettings || {};
      
      // Merge with defaults and validate
      settings = this.mergeWithDefaults(settings);
      settings = this.validateSettings(settings);
      
      // Check if migration is needed
      if (this.needsMigration(settings)) {
        settings = await this.migrateSettings(settings);
      }
      
      // Update last sync timestamp
      settings.lastSync = new Date().toISOString();
      
      // Save validated settings
      await this.saveSettings(settings);
      
      return settings;
      
    } catch (error) {
      console.error('TutorAI: Failed to load settings:', error);
      return this.defaultSettings;
    }
  }

  mergeWithDefaults(settings) {
    const merged = { ...this.defaultSettings };
    
    for (const [key, value] of Object.entries(settings)) {
      if (key in this.defaultSettings) {
        merged[key] = value;
      }
    }
    
    return merged;
  }

  validateSettings(settings) {
    const validated = { ...settings };
    
    for (const [key, schema] of Object.entries(this.settingsSchema)) {
      const value = validated[key];
      
      // Type validation
      if (typeof value !== schema.type) {
        console.warn(`TutorAI: Invalid type for ${key}, using default`);
        validated[key] = schema.default;
        continue;
      }
      
      // Custom validation
      if (schema.validate && !schema.validate(value)) {
        console.warn(`TutorAI: Invalid value for ${key}, using default`);
        validated[key] = schema.default;
      }
    }
    
    return validated;
  }

  needsMigration(settings) {
    const currentVersion = this.defaultSettings.migrationVersion;
    const settingsVersion = settings.migrationVersion || '1.0.0';
    
    return this.compareVersions(currentVersion, settingsVersion) > 0;
  }

  async migrateSettings(settings) {
    const currentVersion = settings.migrationVersion || '1.0.0';
    let migrated = { ...settings };
    
    console.log(`TutorAI: Migrating settings from ${currentVersion}`);
    
    // Version-specific migrations
    if (this.compareVersions('1.1.0', currentVersion) > 0) {
      // Migration for v1.1.0
      migrated.animationSpeed = migrated.animationSpeed || 'normal';
      migrated.tutorialDelay = migrated.tutorialDelay || 2000;
      migrated.migrationVersion = '1.1.0';
    }
    
    if (this.compareVersions('1.2.0', currentVersion) > 0) {
      // Migration for v1.2.0
      migrated.analyticsEnabled = migrated.analyticsEnabled !== false;
      migrated.errorReportingEnabled = migrated.errorReportingEnabled !== false;
      migrated.migrationVersion = '1.2.0';
    }
    
    // Update to current version
    migrated.migrationVersion = this.defaultSettings.migrationVersion;
    
    // Create backup before migration
    await this.createBackup(settings, `pre-migration-${currentVersion}`);
    
    console.log('TutorAI: Settings migration completed');
    return migrated;
  }

  compareVersions(a, b) {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);
    
    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;
      
      if (aPart > bPart) return 1;
      if (aPart < bPart) return -1;
    }
    
    return 0;
  }

  async saveSettings(settings) {
    try {
      // Validate before saving
      const validatedSettings = this.validateSettings(settings);
      
      // Update sync timestamp
      validatedSettings.lastSync = new Date().toISOString();
      
      // Save to sync storage
      await chrome.storage.sync.set({ tutorAISettings: validatedSettings });
      
      // Notify listeners
      this.notifySettingsChanged(validatedSettings);
      
      return validatedSettings;
      
    } catch (error) {
      console.error('TutorAI: Failed to save settings:', error);
      throw error;
    }
  }

  async updateSetting(key, value) {
    try {
      const currentSettings = await this.loadSettings();
      currentSettings[key] = value;
      
      return await this.saveSettings(currentSettings);
      
    } catch (error) {
      console.error(`TutorAI: Failed to update setting ${key}:`, error);
      throw error;
    }
  }

  async updateSettings(updates) {
    try {
      const currentSettings = await this.loadSettings();
      const updatedSettings = { ...currentSettings, ...updates };
      
      return await this.saveSettings(updatedSettings);
      
    } catch (error) {
      console.error('TutorAI: Failed to update settings:', error);
      throw error;
    }
  }

  async resetSettings() {
    try {
      // Create backup before reset
      const currentSettings = await this.loadSettings();
      await this.createBackup(currentSettings, 'pre-reset');
      
      // Reset to defaults
      const resetSettings = { ...this.defaultSettings };
      resetSettings.deviceId = currentSettings.deviceId; // Preserve device ID
      
      return await this.saveSettings(resetSettings);
      
    } catch (error) {
      console.error('TutorAI: Failed to reset settings:', error);
      throw error;
    }
  }

  // Backup and restore functionality
  async createBackup(settings = null, label = null) {
    try {
      if (!settings) {
        settings = await this.loadSettings();
      }
      
      const backupId = label || `backup-${Date.now()}`;
      const backup = {
        id: backupId,
        settings: settings,
        timestamp: new Date().toISOString(),
        version: settings.version || '1.0.0'
      };
      
      // Get existing backups
      const result = await chrome.storage.local.get(['settingsBackups']);
      const backups = result.settingsBackups || [];
      
      // Add new backup
      backups.push(backup);
      
      // Keep only last 10 backups
      if (backups.length > 10) {
        backups.splice(0, backups.length - 10);
      }
      
      // Save backups
      await chrome.storage.local.set({ settingsBackups: backups });
      
      console.log(`TutorAI: Settings backup created: ${backupId}`);
      return backupId;
      
    } catch (error) {
      console.error('TutorAI: Failed to create backup:', error);
      throw error;
    }
  }

  async getBackups() {
    try {
      const result = await chrome.storage.local.get(['settingsBackups']);
      return result.settingsBackups || [];
    } catch (error) {
      console.error('TutorAI: Failed to get backups:', error);
      return [];
    }
  }

  async restoreBackup(backupId) {
    try {
      const backups = await this.getBackups();
      const backup = backups.find(b => b.id === backupId);
      
      if (!backup) {
        throw new Error(`Backup ${backupId} not found`);
      }
      
      // Create current backup before restore
      await this.createBackup(null, 'pre-restore');
      
      // Restore settings
      const restoredSettings = this.validateSettings(backup.settings);
      await this.saveSettings(restoredSettings);
      
      console.log(`TutorAI: Settings restored from backup: ${backupId}`);
      return restoredSettings;
      
    } catch (error) {
      console.error('TutorAI: Failed to restore backup:', error);
      throw error;
    }
  }

  setupAutoBackup() {
    // Create backup every 24 hours
    this.backupInterval = setInterval(async () => {
      try {
        await this.createBackup();
      } catch (error) {
        console.error('TutorAI: Auto backup failed:', error);
      }
    }, 24 * 60 * 60 * 1000);
  }

  setupSyncListeners() {
    // Listen for storage changes
    chrome.storage.onChanged.addListener((changes, namespace) => {
      if (namespace === 'sync' && changes.tutorAISettings) {
        this.handleSyncChange(changes.tutorAISettings);
      }
    });
  }

  handleSyncChange(change) {
    if (this.syncInProgress) return;
    
    console.log('TutorAI: Settings synced from another device');
    this.notifySettingsChanged(change.newValue);
  }

  notifySettingsChanged(settings) {
    // Broadcast to all tabs
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, {
          type: 'SETTINGS_CHANGED',
          data: settings
        }).catch(() => {
          // Tab might not have content script, ignore
        });
      });
    });
  }

  // Export/Import functionality
  async exportSettings() {
    try {
      const settings = await this.loadSettings();
      const exportData = {
        settings: settings,
        backups: await this.getBackups(),
        exportDate: new Date().toISOString(),
        version: settings.version
      };
      
      return JSON.stringify(exportData, null, 2);
      
    } catch (error) {
      console.error('TutorAI: Failed to export settings:', error);
      throw error;
    }
  }

  async importSettings(importData) {
    try {
      const data = JSON.parse(importData);
      
      if (!data.settings) {
        throw new Error('Invalid import data: missing settings');
      }
      
      // Create backup before import
      await this.createBackup(null, 'pre-import');
      
      // Validate and import settings
      const importedSettings = this.validateSettings(data.settings);
      await this.saveSettings(importedSettings);
      
      // Import backups if available
      if (data.backups && Array.isArray(data.backups)) {
        await chrome.storage.local.set({ settingsBackups: data.backups });
      }
      
      console.log('TutorAI: Settings imported successfully');
      return importedSettings;
      
    } catch (error) {
      console.error('TutorAI: Failed to import settings:', error);
      throw error;
    }
  }

  // Cleanup
  destroy() {
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TutorAISettingsManager;
} else {
  window.TutorAISettingsManager = TutorAISettingsManager;
}
