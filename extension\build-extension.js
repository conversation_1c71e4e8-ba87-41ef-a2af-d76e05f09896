/**
 * Build Script for TutorAI Browser Extension
 * Copies necessary files and creates distribution package
 */

const fs = require('fs');
const path = require('path');

class ExtensionBuilder {
  constructor() {
    this.sourceDir = __dirname;
    this.distDir = path.join(__dirname, 'dist');
    this.iconsDir = path.join(__dirname, 'icons');
  }

  async build() {
    console.log('🚀 Building TutorAI Browser Extension...');

    // Clean and create dist directory
    await this.cleanDist();
    await this.createDist();

    // Copy extension files
    await this.copyFiles();

    // Create icons
    await this.createIcons();

    // Create package
    await this.createPackage();

    console.log('✅ Extension built successfully!');
    console.log(`📦 Distribution files created in: ${this.distDir}`);
  }

  async cleanDist() {
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
  }

  async createDist() {
    fs.mkdirSync(this.distDir, { recursive: true });
    fs.mkdirSync(path.join(this.distDir, 'icons'), { recursive: true });
  }

  async copyFiles() {
    const filesToCopy = [
      'manifest.json',
      'popup.html',
      'popup.css',
      'popup.js',
      'content-script.js',
      'content-styles.css',
      'ag-ui-client.js',
      'background.js'
    ];

    for (const file of filesToCopy) {
      const sourcePath = path.join(this.sourceDir, file);
      const destPath = path.join(this.distDir, file);

      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, destPath);
        console.log(`📄 Copied: ${file}`);
      } else {
        console.warn(`⚠️  File not found: ${file}`);
      }
    }
  }

  async createIcons() {
    // Create simple SVG icons if they don't exist
    const iconSizes = [16, 32, 48, 128];
    
    for (const size of iconSizes) {
      const iconPath = path.join(this.distDir, 'icons', `icon-${size}.png`);
      
      if (!fs.existsSync(iconPath)) {
        // Create a simple SVG icon and convert to PNG
        const svgIcon = this.createSVGIcon(size);
        
        // For now, just create a placeholder file
        // In a real implementation, you'd use a library like sharp or canvas to convert SVG to PNG
        fs.writeFileSync(iconPath.replace('.png', '.svg'), svgIcon);
        console.log(`🎨 Created icon: icon-${size}.svg (placeholder)`);
      }
    }
  }

  createSVGIcon(size) {
    return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="#3b82f6"/>
  <text x="50%" y="50%" text-anchor="middle" dy="0.35em" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.6}" font-weight="bold">T</text>
</svg>`;
  }

  async createPackage() {
    const packageInfo = {
      name: 'TutorAI Browser Extension',
      version: '1.0.0',
      description: 'AI-powered web tutoring extension',
      files: fs.readdirSync(this.distDir),
      buildDate: new Date().toISOString()
    };

    fs.writeFileSync(
      path.join(this.distDir, 'package-info.json'),
      JSON.stringify(packageInfo, null, 2)
    );

    console.log('📋 Package info created');
  }
}

// Run build if called directly
if (require.main === module) {
  const builder = new ExtensionBuilder();
  builder.build().catch(console.error);
}

module.exports = ExtensionBuilder;
