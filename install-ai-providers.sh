#!/bin/bash

# TutorAI AI Providers Installation Script
echo "🤖 Installing AI Provider SDKs for TutorAI..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install Node.js and npm first."
    exit 1
fi

print_status "Starting AI provider installation..."

# Install core AI provider SDKs
print_status "Installing OpenAI SDK..."
npm install openai
if [ $? -eq 0 ]; then
    print_success "OpenAI SDK installed successfully"
else
    print_error "Failed to install OpenAI SDK"
fi

print_status "Installing Anthropic SDK..."
npm install @anthropic-ai/sdk
if [ $? -eq 0 ]; then
    print_success "Anthropic SDK installed successfully"
else
    print_error "Failed to install Anthropic SDK"
fi

print_status "Installing Groq SDK..."
npm install groq-sdk
if [ $? -eq 0 ]; then
    print_success "Groq SDK installed successfully"
else
    print_error "Failed to install Groq SDK"
fi

print_status "Installing Axios for OpenRouter..."
npm install axios
if [ $? -eq 0 ]; then
    print_success "Axios installed successfully"
else
    print_error "Failed to install Axios"
fi

# Install optional AG-UI packages (with fallback handling)
print_status "Installing AG-UI packages (optional)..."
npm install @ag-ui/core @ag-ui/client @ag-ui/encoder
if [ $? -eq 0 ]; then
    print_success "AG-UI packages installed successfully"
else
    print_warning "AG-UI packages failed to install (using fallback implementation)"
fi

# Install additional dependencies
print_status "Installing additional dependencies..."
npm install @types/node
if [ $? -eq 0 ]; then
    print_success "Additional dependencies installed"
else
    print_warning "Some additional dependencies failed to install"
fi

# Run npm audit fix
print_status "Running security audit..."
npm audit fix --force

print_success "🎉 AI Provider installation complete!"

echo ""
echo "📋 Next steps:"
echo "1. Copy .env.example to .env"
echo "2. Add your API keys:"
echo "   - OPENAI_API_KEY=sk-your-openai-key"
echo "   - ANTHROPIC_API_KEY=sk-ant-your-anthropic-key"
echo "   - GROQ_API_KEY=gsk_your-groq-key"
echo "   - OPENROUTER_API_KEY=sk-or-your-openrouter-key"
echo "3. Run: npm run dev"
echo ""

# Check which providers are available
echo "🔍 Checking installed providers:"

if npm list openai &> /dev/null; then
    print_success "✅ OpenAI SDK - Ready"
else
    print_error "❌ OpenAI SDK - Not installed"
fi

if npm list @anthropic-ai/sdk &> /dev/null; then
    print_success "✅ Anthropic SDK - Ready"
else
    print_error "❌ Anthropic SDK - Not installed"
fi

if npm list groq-sdk &> /dev/null; then
    print_success "✅ Groq SDK - Ready"
else
    print_error "❌ Groq SDK - Not installed"
fi

if npm list axios &> /dev/null; then
    print_success "✅ Axios (OpenRouter) - Ready"
else
    print_error "❌ Axios (OpenRouter) - Not installed"
fi

echo ""
echo "🚀 Your TutorAI app now supports multiple AI providers with automatic fallback!"
echo "   Primary: OpenAI GPT-4"
echo "   Fallback: Anthropic Claude, Groq Mixtral, OpenRouter"
echo ""
echo "💡 Pro tip: Configure multiple providers for maximum reliability!"

# Create a simple test script
cat > test-ai-providers.js << 'EOF'
// Quick test script for AI providers
const { getAIService } = require('./src/lib/ai-providers');

async function testProviders() {
  console.log('🧪 Testing AI providers...');
  
  const aiService = getAIService();
  
  // Test with a simple prompt
  const messageId = 'test_' + Date.now();
  
  aiService.onEvent((event) => {
    console.log('Event:', event.type, event.delta || '');
  });
  
  try {
    await aiService.streamCompletion('Say hello!', messageId);
    console.log('✅ AI providers working correctly!');
  } catch (error) {
    console.error('❌ AI provider test failed:', error.message);
  }
}

// Uncomment to run test
// testProviders();
EOF

print_success "Created test-ai-providers.js for testing"

echo ""
echo "🎯 Installation Summary:"
echo "   ✅ Multi-provider AI service implemented"
echo "   ✅ Automatic fallback between providers"
echo "   ✅ Dynamic imports (works without packages)"
echo "   ✅ Production-ready error handling"
echo "   ✅ Streaming support for all providers"
echo ""
echo "Ready to launch! 🚀"
