import { connectToDatabase, disconnectFromDatabase, isDatabaseConnected } from './database';
import { DATABASE_CONFIG, buildDatabaseUrl } from './config';

/**
 * Script to check database connection
 * Can be run directly with: npx tsx src/lib/db-check.ts
 */
async function checkDatabaseConnection() {
  console.log('Checking database connection...');
  
  try {
    // Check if database configuration is set
    if (!DATABASE_CONFIG.URL && (!DATABASE_CONFIG.HOST || !DATABASE_CONFIG.USER)) {
      console.error('ERROR: Database configuration is not set');
      console.error('Please check your .env file in the project root directory');
      console.error('You can set either DATABASE_URL or individual DB_* variables');
      process.exit(1);
    }
    
    // Display configuration being used
    const dbUrl = buildDatabaseUrl();
    console.log(`Using database configuration: ${maskDatabaseUrl(dbUrl)}`);
    
    // Try to connect to the database
    await connectToDatabase();
    
    // Verify connection with a simple query
    const isConnected = await isDatabaseConnected();
    
    if (isConnected) {
      console.log('✅ Database connection successful');
      console.log(`Host: ${DATABASE_CONFIG.HOST}`);
      console.log(`Port: ${DATABASE_CONFIG.PORT}`);
      console.log(`Database: ${DATABASE_CONFIG.NAME}`);
      console.log(`User: ${DATABASE_CONFIG.USER}`);
    } else {
      console.error('❌ Database connection failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Database connection error:', error);
    process.exit(1);
  } finally {
    // Always disconnect properly
    await disconnectFromDatabase();
  }
}

/**
 * Mask sensitive information in the database URL for logging
 */
function maskDatabaseUrl(url: string): string {
  try {
    const maskedUrl = new URL(url);
    if (maskedUrl.password) {
      maskedUrl.password = '********';
    }
    return maskedUrl.toString();
  } catch (error) {
    return 'Invalid database URL format';
  }
}

// Run the check if this file is executed directly
if (require.main === module) {
  checkDatabaseConnection()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
} 

export { checkDatabaseConnection }; 