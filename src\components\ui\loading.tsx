"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Loader2, <PERSON><PERSON><PERSON> } from "lucide-react";

interface SpinnerProps {
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

export function Spinner({ size = "md", className }: SpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12",
  };

  return (
    <Loader2
      className={cn(
        "animate-spin text-muted-foreground",
        sizeClasses[size],
        className
      )}
    />
  );
}

interface LoadingDotsProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function LoadingDots({ size = "md", className }: LoadingDotsProps) {
  const sizeClasses = {
    sm: "h-1 w-1",
    md: "h-2 w-2",
    lg: "h-3 w-3",
  };

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            "bg-current rounded-full animate-pulse",
            sizeClasses[size]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: "1s",
          }}
        />
      ))}
    </div>
  );
}

interface PulseProps {
  className?: string;
  children?: React.ReactNode;
}

export function Pulse({ className, children }: PulseProps) {
  return (
    <div className={cn("animate-pulse", className)}>
      {children || <div className="bg-muted rounded h-4 w-full" />}
    </div>
  );
}

interface SkeletonProps {
  className?: string;
  lines?: number;
  avatar?: boolean;
}

export function Skeleton({ className, lines = 3, avatar = false }: SkeletonProps) {
  return (
    <div className={cn("space-y-3", className)}>
      {avatar && (
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-muted rounded-full animate-pulse" />
          <div className="space-y-2 flex-1">
            <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
            <div className="h-3 bg-muted rounded animate-pulse w-1/6" />
          </div>
        </div>
      )}
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            className={cn(
              "h-4 bg-muted rounded animate-pulse",
              i === lines - 1 ? "w-3/4" : "w-full"
            )}
          />
        ))}
      </div>
    </div>
  );
}

interface CardSkeletonProps {
  className?: string;
  showImage?: boolean;
  showAvatar?: boolean;
}

export function CardSkeleton({ 
  className, 
  showImage = false, 
  showAvatar = false 
}: CardSkeletonProps) {
  return (
    <div className={cn("border rounded-lg p-4 sm:p-6 space-y-4", className)}>
      {showImage && (
        <div className="aspect-video bg-muted rounded-lg animate-pulse" />
      )}
      
      <div className="space-y-3">
        <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
        <div className="space-y-2">
          <div className="h-4 bg-muted rounded animate-pulse" />
          <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
        </div>
      </div>

      {showAvatar && (
        <div className="flex items-center space-x-3 pt-2">
          <div className="h-8 w-8 bg-muted rounded-full animate-pulse" />
          <div className="space-y-1 flex-1">
            <div className="h-3 bg-muted rounded animate-pulse w-1/4" />
            <div className="h-3 bg-muted rounded animate-pulse w-1/6" />
          </div>
        </div>
      )}
    </div>
  );
}

interface LoadingStateProps {
  loading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  skeleton?: boolean;
  lines?: number;
}

export function LoadingState({ 
  loading, 
  children, 
  fallback, 
  skeleton = false,
  lines = 3 
}: LoadingStateProps) {
  if (loading) {
    if (fallback) return <>{fallback}</>;
    if (skeleton) return <Skeleton lines={lines} />;
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return <>{children}</>;
}

interface LoadingOverlayProps {
  loading: boolean;
  children: React.ReactNode;
  className?: string;
  message?: string;
}

export function LoadingOverlay({ 
  loading, 
  children, 
  className,
  message = "Loading..." 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {loading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          <div className="flex flex-col items-center space-y-3">
            <Sparkles className="h-8 w-8 text-primary animate-pulse" />
            <p className="text-sm text-muted-foreground">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface ProgressBarProps {
  progress: number;
  className?: string;
  showPercentage?: boolean;
  animated?: boolean;
}

export function ProgressBar({ 
  progress, 
  className, 
  showPercentage = false,
  animated = true 
}: ProgressBarProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
        <div
          className={cn(
            "h-full bg-primary rounded-full transition-all duration-500 ease-out",
            animated && "animate-pulse"
          )}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
      {showPercentage && (
        <p className="text-xs text-muted-foreground text-center">
          {Math.round(progress)}%
        </p>
      )}
    </div>
  );
}

interface LoadingButtonProps {
  loading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}

export function LoadingButton({ 
  loading, 
  children, 
  loadingText = "Loading...",
  className,
  disabled,
  onClick 
}: LoadingButtonProps) {
  return (
    <button
      className={cn(
        "inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none transition-colors",
        className
      )}
      disabled={loading || disabled}
      onClick={onClick}
    >
      {loading && <Spinner size="sm" className="text-current" />}
      {loading ? loadingText : children}
    </button>
  );
}
