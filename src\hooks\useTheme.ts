import { useTheme as useNextTheme } from 'next-themes';
import { useEffect, useState } from 'react';

export interface ThemeConfig {
  theme: string | undefined;
  resolvedTheme: string | undefined;
  setTheme: (theme: string) => void;
  systemTheme: string | undefined;
  themes: string[];
  forcedTheme: string | undefined;
}

export function useTheme(): ThemeConfig & {
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
  toggleTheme: () => void;
  setLightTheme: () => void;
  setDarkTheme: () => void;
  setSystemTheme: () => void;
} {
  const themeConfig = useNextTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const isDark = mounted && themeConfig.resolvedTheme === 'dark';
  const isLight = mounted && themeConfig.resolvedTheme === 'light';
  const isSystem = mounted && themeConfig.theme === 'system';

  const toggleTheme = () => {
    if (themeConfig.theme === 'light') {
      themeConfig.setTheme('dark');
    } else if (themeConfig.theme === 'dark') {
      themeConfig.setTheme('system');
    } else {
      themeConfig.setTheme('light');
    }
  };

  const setLightTheme = () => themeConfig.setTheme('light');
  const setDarkTheme = () => themeConfig.setTheme('dark');
  const setSystemTheme = () => themeConfig.setTheme('system');

  return {
    ...themeConfig,
    isDark,
    isLight,
    isSystem,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
  };
}

// Hook for theme-aware styling
export function useThemeAwareStyles() {
  const { isDark, isLight, resolvedTheme } = useTheme();

  const getThemeClass = (lightClass: string, darkClass: string) => {
    return isDark ? darkClass : lightClass;
  };

  const getThemeValue = <T>(lightValue: T, darkValue: T): T => {
    return isDark ? darkValue : lightValue;
  };

  const getGlassClass = () => {
    return isDark ? 'glass-dark' : 'glass-light';
  };

  const getShadowClass = (size: 'sm' | 'md' | 'lg' = 'md') => {
    const baseClass = 'shadow-theme';
    return size === 'lg' ? `${baseClass}-lg` : baseClass;
  };

  return {
    isDark,
    isLight,
    resolvedTheme,
    getThemeClass,
    getThemeValue,
    getGlassClass,
    getShadowClass,
  };
}

// Hook for managing theme transitions
export function useThemeTransition() {
  const [isTransitioning, setIsTransitioning] = useState(false);

  const disableTransitions = () => {
    setIsTransitioning(true);
    
    // Add class to disable transitions
    document.documentElement.classList.add('theme-transitioning');
    
    // Re-enable transitions after a short delay
    setTimeout(() => {
      document.documentElement.classList.remove('theme-transitioning');
      setIsTransitioning(false);
    }, 100);
  };

  return {
    isTransitioning,
    disableTransitions,
  };
}

// Hook for theme persistence
export function useThemePersistence() {
  const { theme, setTheme } = useTheme();

  const saveThemePreference = (newTheme: string) => {
    try {
      localStorage.setItem('theme-preference', newTheme);
      setTheme(newTheme);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
      setTheme(newTheme);
    }
  };

  const getStoredTheme = (): string | null => {
    try {
      return localStorage.getItem('theme-preference');
    } catch (error) {
      console.warn('Failed to get stored theme:', error);
      return null;
    }
  };

  const clearThemePreference = () => {
    try {
      localStorage.removeItem('theme-preference');
    } catch (error) {
      console.warn('Failed to clear theme preference:', error);
    }
  };

  return {
    theme,
    saveThemePreference,
    getStoredTheme,
    clearThemePreference,
  };
}

// Hook for system theme detection
export function useSystemTheme() {
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const updateSystemTheme = () => {
      setSystemTheme(mediaQuery.matches ? 'dark' : 'light');
    };

    updateSystemTheme();
    mediaQuery.addEventListener('change', updateSystemTheme);

    return () => {
      mediaQuery.removeEventListener('change', updateSystemTheme);
    };
  }, []);

  return systemTheme;
}

// Hook for theme-based animations
export function useThemeAnimations() {
  const { isDark } = useTheme();
  const [previousTheme, setPreviousTheme] = useState<boolean | null>(null);
  const [isThemeChanging, setIsThemeChanging] = useState(false);

  useEffect(() => {
    if (previousTheme !== null && previousTheme !== isDark) {
      setIsThemeChanging(true);
      
      // Reset animation state after transition
      const timer = setTimeout(() => {
        setIsThemeChanging(false);
      }, 200);

      return () => clearTimeout(timer);
    }
    
    setPreviousTheme(isDark);
  }, [isDark, previousTheme]);

  return {
    isThemeChanging,
    isDark,
    previousTheme,
  };
}

// Hook for theme-aware colors
export function useThemeColors() {
  const { isDark, resolvedTheme } = useTheme();

  const colors = {
    primary: isDark ? '#ffffff' : '#000000',
    secondary: isDark ? '#1f2937' : '#f3f4f6',
    accent: isDark ? '#3b82f6' : '#2563eb',
    muted: isDark ? '#374151' : '#9ca3af',
    background: isDark ? '#111827' : '#ffffff',
    foreground: isDark ? '#f9fafb' : '#111827',
    border: isDark ? '#374151' : '#e5e7eb',
    success: isDark ? '#10b981' : '#059669',
    warning: isDark ? '#f59e0b' : '#d97706',
    error: isDark ? '#ef4444' : '#dc2626',
  };

  const getColor = (colorName: keyof typeof colors) => colors[colorName];

  const getRgbColor = (colorName: keyof typeof colors) => {
    const hex = colors[colorName];
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };

  const getColorWithOpacity = (colorName: keyof typeof colors, opacity: number) => {
    const rgb = getRgbColor(colorName);
    return rgb ? `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity})` : colors[colorName];
  };

  return {
    colors,
    getColor,
    getRgbColor,
    getColorWithOpacity,
    isDark,
    resolvedTheme,
  };
}
