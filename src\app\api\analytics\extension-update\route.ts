/**
 * Extension Update Analytics API
 * Tracks successful extension updates and migration patterns
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting for analytics
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // limit it to 500 requests per minute
});

const UpdateAnalyticsSchema = z.object({
  extensionId: z.string(),
  previousVersion: z.string(),
  currentVersion: z.string(),
  updateMethod: z.enum(['auto', 'manual', 'forced']),
  timestamp: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip ?? '127.0.0.1';
    const { success } = await limiter.check(10, ip); // 10 requests per minute per IP
    
    if (!success) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const validatedData = UpdateAnalyticsSchema.parse(body);

    // Store update analytics
    await prisma.analytics.create({
      data: {
        userId: 'system', // System-generated event
        action: 'extension_update_completed',
        metadata: {
          extensionId: validatedData.extensionId,
          previousVersion: validatedData.previousVersion,
          currentVersion: validatedData.currentVersion,
          updateMethod: validatedData.updateMethod,
          timestamp: validatedData.timestamp,
        },
      },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Extension update analytics error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
