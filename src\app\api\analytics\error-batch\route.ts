/**
 * Error Batch Analytics API
 * Handles batch error reports from browser extension
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting for batch error reports
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // limit it to 500 requests per minute
});

const ErrorBatchSchema = z.object({
  extensionId: z.string(),
  errors: z.array(z.object({
    id: z.string(),
    timestamp: z.number(),
    type: z.string(),
    message: z.string(),
    stack: z.string().optional(),
    filename: z.string().optional(),
    lineno: z.number().optional(),
    colno: z.number().optional(),
    severity: z.enum(['critical', 'high', 'medium', 'low', 'info']),
    category: z.string(),
    context: z.record(z.any()),
    userAgent: z.string(),
    url: z.string(),
  })),
  timestamp: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip ?? '127.0.0.1';
    const { success } = await limiter.check(10, ip); // 10 batch requests per minute per IP
    
    if (!success) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const validatedData = ErrorBatchSchema.parse(body);

    // Limit batch size
    if (validatedData.errors.length > 50) {
      return NextResponse.json(
        { error: 'Batch size too large. Maximum 50 errors per batch.' },
        { status: 400 }
      );
    }

    // Process errors in batch
    const analyticsData = validatedData.errors.map(error => ({
      userId: 'system',
      action: 'extension_error_report',
      metadata: {
        extensionId: validatedData.extensionId,
        errorId: error.id,
        errorType: error.type,
        errorMessage: error.message,
        errorSeverity: error.severity,
        errorCategory: error.category,
        errorStack: error.stack,
        errorContext: error.context,
        userAgent: error.userAgent,
        url: error.url,
        batchTimestamp: validatedData.timestamp,
        originalTimestamp: new Date(error.timestamp).toISOString(),
      },
    }));

    // Bulk insert
    await prisma.analytics.createMany({
      data: analyticsData,
    });

    // Process critical errors
    const criticalErrors = validatedData.errors.filter(error => error.severity === 'critical');
    if (criticalErrors.length > 0) {
      await handleCriticalErrorsBatch(validatedData.extensionId, criticalErrors);
    }

    // Analyze error patterns
    await analyzeErrorBatch(validatedData);

    return NextResponse.json({ 
      success: true, 
      processed: validatedData.errors.length,
      critical: criticalErrors.length 
    });

  } catch (error) {
    console.error('Error batch analytics error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleCriticalErrorsBatch(extensionId: string, criticalErrors: any[]) {
  try {
    console.error('Critical errors batch received:', {
      extensionId,
      count: criticalErrors.length,
      types: criticalErrors.map(e => e.type),
      categories: criticalErrors.map(e => e.category),
    });

    // Create audit log entries for critical errors
    const auditData = criticalErrors.map(error => ({
      action: 'critical_extension_error_batch',
      resource: 'extension',
      resourceId: extensionId,
      metadata: {
        errorId: error.id,
        errorType: error.type,
        errorMessage: error.message,
        errorCategory: error.category,
        errorContext: error.context,
        userAgent: error.userAgent,
        batchSize: criticalErrors.length,
      },
      ipAddress: '127.0.0.1',
      userAgent: error.userAgent,
    }));

    await prisma.auditLog.createMany({
      data: auditData,
    });

  } catch (error) {
    console.error('Failed to handle critical errors batch:', error);
  }
}

async function analyzeErrorBatch(batchData: any) {
  try {
    const errors = batchData.errors;
    
    // Analyze error distribution
    const errorStats = {
      total: errors.length,
      bySeverity: {},
      byCategory: {},
      byType: {},
      timeSpan: {
        earliest: Math.min(...errors.map(e => e.timestamp)),
        latest: Math.max(...errors.map(e => e.timestamp)),
      },
    };

    // Count by severity
    errors.forEach(error => {
      errorStats.bySeverity[error.severity] = (errorStats.bySeverity[error.severity] || 0) + 1;
      errorStats.byCategory[error.category] = (errorStats.byCategory[error.category] || 0) + 1;
      errorStats.byType[error.type] = (errorStats.byType[error.type] || 0) + 1;
    });

    // Check for concerning patterns
    const concerns = [];

    // High critical error ratio
    const criticalRatio = (errorStats.bySeverity.critical || 0) / errorStats.total;
    if (criticalRatio > 0.1) { // More than 10% critical
      concerns.push({
        type: 'high_critical_ratio',
        value: criticalRatio,
        threshold: 0.1,
      });
    }

    // High error frequency
    const timeSpanMs = errorStats.timeSpan.latest - errorStats.timeSpan.earliest;
    const errorRate = errorStats.total / (timeSpanMs / 1000); // errors per second
    if (errorRate > 0.1) { // More than 0.1 errors per second
      concerns.push({
        type: 'high_error_rate',
        value: errorRate,
        threshold: 0.1,
      });
    }

    // Dominant error category
    const maxCategoryCount = Math.max(...Object.values(errorStats.byCategory));
    const dominantCategoryRatio = maxCategoryCount / errorStats.total;
    if (dominantCategoryRatio > 0.7) { // More than 70% from one category
      const dominantCategory = Object.keys(errorStats.byCategory).find(
        key => errorStats.byCategory[key] === maxCategoryCount
      );
      concerns.push({
        type: 'dominant_error_category',
        category: dominantCategory,
        ratio: dominantCategoryRatio,
        threshold: 0.7,
      });
    }

    // Log analysis results if there are concerns
    if (concerns.length > 0) {
      console.warn('Error batch analysis concerns:', {
        extensionId: batchData.extensionId,
        stats: errorStats,
        concerns: concerns,
      });

      // Store analysis results
      await prisma.analytics.create({
        data: {
          userId: 'system',
          action: 'extension_error_batch_analysis',
          metadata: {
            extensionId: batchData.extensionId,
            errorStats: errorStats,
            concerns: concerns,
            batchTimestamp: batchData.timestamp,
          },
        },
      });
    }

  } catch (error) {
    console.error('Failed to analyze error batch:', error);
  }
}
