import { OnboardingFlow } from './OnboardingProvider';

export const mainTourFlow: OnboardingFlow = {
  id: 'main-tour',
  name: 'Main Tour',
  description: 'Get familiar with the core features and interface',
  autoStart: false,
  persistent: false,
  steps: [
    {
      id: 'welcome',
      title: 'Welcome to TutorAI!',
      description: 'This is your AI-powered learning companion. Let\'s explore the main features together.',
      position: 'center',
      action: 'none',
    },
    {
      id: 'navigation',
      title: 'Navigation Menu',
      description: 'Use this navigation to access different sections like Features, Demo, Dashboard, and Pricing.',
      target: 'nav[role="navigation"]',
      position: 'bottom',
      action: 'none',
    },
    {
      id: 'theme-switcher',
      title: 'Theme Switcher',
      description: 'Toggle between light, dark, and system themes to match your preference.',
      target: '[aria-label*="theme"]',
      position: 'bottom',
      action: 'click',
      optional: true,
    },
    {
      id: 'hero-section',
      title: 'Main Features',
      description: 'This section highlights our key features: AI explanations, voice synthesis, and interactive guidance.',
      target: 'section:first-of-type',
      position: 'center',
      action: 'none',
    },
    {
      id: 'cta-buttons',
      title: 'Get Started',
      description: 'Click "Get Started Free" to create your account and begin your learning journey.',
      target: 'a[href="/auth/signup"]',
      position: 'top',
      action: 'hover',
    },
    {
      id: 'features-section',
      title: 'Feature Showcase',
      description: 'Scroll down to explore detailed information about each feature and how they can help you learn.',
      target: '#features',
      position: 'top',
      action: 'none',
    },
    {
      id: 'demo-section',
      title: 'Interactive Demo',
      description: 'Try out the interactive demo to see how the extension works on real webpages.',
      target: '#demo',
      position: 'top',
      action: 'none',
    },
    {
      id: 'tour-complete',
      title: 'Tour Complete!',
      description: 'You\'ve completed the main tour. Ready to start using TutorAI? Create your account to get started.',
      position: 'center',
      action: 'none',
    },
  ],
};

export const aiFeaturesFlow: OnboardingFlow = {
  id: 'ai-features',
  name: 'AI Features Tour',
  description: 'Learn how to use AI-powered explanations and voice synthesis',
  autoStart: false,
  persistent: false,
  steps: [
    {
      id: 'ai-intro',
      title: 'AI-Powered Learning',
      description: 'TutorAI uses advanced AI to provide real-time explanations for any webpage content.',
      position: 'center',
      action: 'none',
    },
    {
      id: 'extension-demo',
      title: 'Browser Extension',
      description: 'The extension adds an overlay to any webpage, providing contextual explanations and guidance.',
      target: '[data-testid="extension-demo"]',
      position: 'top',
      action: 'none',
    },
    {
      id: 'voice-synthesis',
      title: 'Voice Synthesis',
      description: 'Click the play button to hear AI explanations read aloud with natural voice synthesis.',
      target: '[aria-label*="play"]',
      position: 'top',
      action: 'click',
      optional: true,
    },
    {
      id: 'interactive-elements',
      title: 'Interactive Elements',
      description: 'Hover over highlighted elements to get instant explanations and learning tips.',
      target: '.highlight-element',
      position: 'right',
      action: 'hover',
      optional: true,
    },
    {
      id: 'ai-complete',
      title: 'AI Features Mastered!',
      description: 'You now know how to use AI explanations and voice synthesis. Try it on any webpage!',
      position: 'center',
      action: 'none',
    },
  ],
};

export const dashboardTourFlow: OnboardingFlow = {
  id: 'dashboard-tour',
  name: 'Dashboard Tour',
  description: 'Explore analytics, settings, and management features',
  autoStart: false,
  persistent: false,
  steps: [
    {
      id: 'dashboard-intro',
      title: 'Your Learning Dashboard',
      description: 'Welcome to your personal dashboard where you can track progress and manage settings.',
      position: 'center',
      action: 'none',
    },
    {
      id: 'stats-cards',
      title: 'Learning Statistics',
      description: 'These cards show your learning progress, including tutorials completed and time spent.',
      target: '[data-testid="stats-cards"]',
      position: 'bottom',
      action: 'none',
    },
    {
      id: 'recent-activity',
      title: 'Recent Activity',
      description: 'View your recent learning activities and track your engagement over time.',
      target: '[data-testid="recent-activity"]',
      position: 'left',
      action: 'none',
    },
    {
      id: 'analytics-charts',
      title: 'Analytics & Insights',
      description: 'Detailed charts and graphs help you understand your learning patterns and progress.',
      target: '[data-testid="analytics-section"]',
      position: 'top',
      action: 'none',
    },
    {
      id: 'settings-access',
      title: 'Settings & Preferences',
      description: 'Customize your learning experience through the settings panel.',
      target: '[aria-label*="settings"]',
      position: 'left',
      action: 'click',
      optional: true,
    },
    {
      id: 'dashboard-complete',
      title: 'Dashboard Tour Complete!',
      description: 'You\'re now familiar with all dashboard features. Start exploring your learning data!',
      position: 'center',
      action: 'none',
    },
  ],
};

export const advancedFeaturesFlow: OnboardingFlow = {
  id: 'advanced-features',
  name: 'Advanced Features',
  description: 'Master advanced customization and integration options',
  autoStart: false,
  persistent: false,
  steps: [
    {
      id: 'advanced-intro',
      title: 'Advanced Features',
      description: 'Unlock the full potential of TutorAI with these advanced features and customizations.',
      position: 'center',
      action: 'none',
    },
    {
      id: 'custom-prompts',
      title: 'Custom AI Prompts',
      description: 'Create custom prompts to tailor AI explanations to your specific learning needs.',
      target: '[data-testid="custom-prompts"]',
      position: 'right',
      action: 'none',
    },
    {
      id: 'integration-options',
      title: 'Third-party Integrations',
      description: 'Connect TutorAI with your favorite learning platforms and note-taking apps.',
      target: '[data-testid="integrations"]',
      position: 'bottom',
      action: 'none',
    },
    {
      id: 'api-access',
      title: 'API Access',
      description: 'Use our API to build custom integrations and extend TutorAI\'s functionality.',
      target: '[data-testid="api-section"]',
      position: 'top',
      action: 'none',
    },
    {
      id: 'advanced-analytics',
      title: 'Advanced Analytics',
      description: 'Access detailed learning analytics and export your data for further analysis.',
      target: '[data-testid="advanced-analytics"]',
      position: 'left',
      action: 'none',
    },
    {
      id: 'advanced-complete',
      title: 'Advanced Features Mastered!',
      description: 'You\'re now a TutorAI power user! Explore these features to enhance your learning experience.',
      position: 'center',
      action: 'none',
    },
  ],
};

// Quick start flow for returning users
export const quickStartFlow: OnboardingFlow = {
  id: 'quick-start',
  name: 'Quick Start',
  description: 'Quick refresher for returning users',
  autoStart: false,
  persistent: true,
  steps: [
    {
      id: 'welcome-back',
      title: 'Welcome Back!',
      description: 'Ready to continue learning? Here\'s a quick reminder of key features.',
      position: 'center',
      action: 'none',
    },
    {
      id: 'new-features',
      title: 'What\'s New',
      description: 'Check out the latest features and improvements we\'ve added since your last visit.',
      target: '[data-testid="whats-new"]',
      position: 'bottom',
      action: 'none',
      optional: true,
    },
    {
      id: 'quick-actions',
      title: 'Quick Actions',
      description: 'Access your most-used features quickly from this panel.',
      target: '[data-testid="quick-actions"]',
      position: 'right',
      action: 'none',
    },
    {
      id: 'quick-start-complete',
      title: 'You\'re All Set!',
      description: 'Continue where you left off and keep learning with TutorAI.',
      position: 'center',
      action: 'none',
    },
  ],
};

// Export all flows
export const allOnboardingFlows = [
  mainTourFlow,
  aiFeaturesFlow,
  dashboardTourFlow,
  advancedFeaturesFlow,
  quickStartFlow,
];

// Helper function to get flow by ID
export function getFlowById(flowId: string): OnboardingFlow | undefined {
  return allOnboardingFlows.find(flow => flow.id === flowId);
}

// Helper function to get recommended flows for new users
export function getRecommendedFlows(): OnboardingFlow[] {
  return [mainTourFlow, aiFeaturesFlow];
}

// Helper function to get flows by difficulty
export function getFlowsByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): OnboardingFlow[] {
  const difficultyMap = {
    beginner: [mainTourFlow, aiFeaturesFlow],
    intermediate: [dashboardTourFlow],
    advanced: [advancedFeaturesFlow],
  };
  
  return difficultyMap[difficulty] || [];
}
