"use client";

import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Volume2,
  VolumeX,
  X,
  MessageCircle,
} from "lucide-react";
import { Tutorial, TutorialStep } from "@/models/Tutorial";

interface TutorialPlayerProps {
  tutorial: Tutorial;
  onComplete: () => void;
  onClose: () => void;
}

export default function TutorialPlayer({
  tutorial,
  onComplete,
  onClose,
}: TutorialPlayerProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);
  const stepRef = useRef<HTMLDivElement>(null);

  const currentStep = tutorial.steps[currentStepIndex];
  const totalSteps = tutorial.steps.length;

  useEffect(() => {
    // Calculate progress
    setProgress(((currentStepIndex + 1) / totalSteps) * 100);
  }, [currentStepIndex, totalSteps]);

  useEffect(() => {
    if (!isPlaying || !currentStep) return;

    // Auto-advance if the step has a duration
    let timer: NodeJS.Timeout;
    if (currentStep.duration) {
      timer = setTimeout(() => {
        handleNextStep();
      }, currentStep.duration);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isPlaying, currentStepIndex, currentStep]);

  useEffect(() => {
    // Position the step tooltip based on the current step's position
    if (stepRef.current && currentStep) {
      const { x, y } = currentStep.position;
        
    }
  }, [currentStep]);

  const handlePreviousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  };

  const handleNextStep = () => {
    if (currentStepIndex < totalSteps - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      // Tutorial completed
      onComplete();
    }
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const renderStepContent = () => {
    if (!currentStep) return null;

    switch (currentStep.action) {
      case "explain":
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{currentStep.title}</h3>
            <p>{currentStep.description}</p>
            <div className="p-4 bg-muted/50 rounded-lg">
              {currentStep.content}
            </div>
          </div>
        );
      case "click":
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{currentStep.title}</h3>
            <p>{currentStep.description}</p>
            <div className="flex items-center gap-2 text-primary">
              <MessageCircle className="h-5 w-5" />
              <span>Click on the highlighted element to continue</span>
            </div>
          </div>
        );
      case "type":
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{currentStep.title}</h3>
            <p>{currentStep.description}</p>
            <div className="p-4 bg-muted/50 rounded-lg">
              <p>Type: {currentStep.content}</p>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{currentStep.title}</h3>
            <p>{currentStep.description}</p>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="relative w-full max-w-lg bg-background rounded-lg shadow-lg overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div>
              <h2 className="text-xl font-bold">{tutorial.title}</h2>
              <p className="text-sm text-muted-foreground">
                Step {currentStepIndex + 1} of {totalSteps}
              </p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="rounded-full"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Progress Bar */}
          <Progress value={progress} className="h-1 w-full" />

          {/* Content */}
          <div className="p-6">
            <div ref={stepRef} className="min-h-[200px]">
              {renderStepContent()}
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between p-4 border-t">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={toggleMute}
                className="rounded-full"
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={togglePlayPause}
                className="rounded-full"
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={handlePreviousStep}
                disabled={currentStepIndex === 0}
                className="rounded-full"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleNextStep}
                className="rounded-full"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
} 