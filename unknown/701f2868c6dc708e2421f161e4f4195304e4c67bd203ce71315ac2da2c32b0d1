import { useState, useEffect, useCallback, useRef } from "react";
import {
  TutorialStep,
  HighlightingConfig,
  UseHighlightingOptions,
  UseHighlightingReturn,
  NavigationState,
  CalculatedPosition,
} from "./types";
import {
  findElement,
  getElementBounds,
  getViewportBounds,
  calculateTooltipPosition,
  scrollElementIntoView,
  debounce,
  isElementVisible,
} from "./utils";

const defaultConfig: HighlightingConfig = {
  theme: "auto",
  animationDuration: 300,
  showProgress: true,
  allowSkip: true,
  keyboardNavigation: true,
  autoAdvance: false,
  autoAdvanceDelay: 5000,
  zIndexBase: 10000,
};

export function useHighlighting({
  steps,
  config: userConfig = defaultConfig,
  onStepChange,
  onComplete,
}: UseHighlightingOptions): UseHighlightingReturn {
  const config = { ...defaultConfig, ...userConfig };
  const [currentStep, setCurrentStep] = useState(-1);
  const [highlightedElement, setHighlightedElement] = useState<Element | null>(
    null,
  );
  const [tooltipPosition, setTooltipPosition] =
    useState<CalculatedPosition | null>(null);
  const [isActive, setIsActive] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const autoAdvanceTimeoutRef = useRef<NodeJS.Timeout>();

  const currentStepData =
    currentStep >= 0 && currentStep < steps.length ? steps[currentStep] : null;

  const navigationState: NavigationState = {
    currentStep,
    totalSteps: steps.length,
    isPlaying: isActive,
    canGoNext: currentStep < steps.length - 1,
    canGoPrevious: currentStep > 0,
  };

  // Update highlighted element and tooltip position
  const updateHighlight = useCallback(async () => {
    if (!currentStepData || !isActive) {
      setHighlightedElement(null);
      setTooltipPosition(null);
      return;
    }

    const element = findElement(currentStepData.selector);
    if (!element) {
      console.warn(
        `Element not found for selector: ${currentStepData.selector}`,
      );
      return;
    }

    setHighlightedElement(element);

    // Scroll element into view if not visible
    if (!isElementVisible(element)) {
      await scrollElementIntoView(element, {
        behavior: "smooth",
        block: "center",
        offset: { x: 0, y: -100 }, // Offset for tooltip space
      });
    }

    // Calculate tooltip position
    const elementBounds = getElementBounds(element);
    const viewport = getViewportBounds();

    // Get tooltip dimensions (estimate if not available)
    const tooltipBounds = {
      width: tooltipRef.current?.offsetWidth || 320,
      height: tooltipRef.current?.offsetHeight || 200,
    };

    const position = calculateTooltipPosition(
      elementBounds,
      tooltipBounds,
      currentStepData.position || { placement: "auto" },
      viewport,
    );

    setTooltipPosition(position);
  }, [currentStepData, isActive]);

  // Debounced update for performance
  const debouncedUpdateHighlight = useCallback(debounce(updateHighlight, 100), [
    updateHighlight,
  ]);

  // Handle window resize and scroll
  useEffect(() => {
    if (!isActive) return;

    const handleResize = () => debouncedUpdateHighlight();
    const handleScroll = () => debouncedUpdateHighlight();

    window.addEventListener("resize", handleResize);
    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isActive, debouncedUpdateHighlight]);

  // Update highlight when step changes
  useEffect(() => {
    updateHighlight();
  }, [updateHighlight]);

  // Handle auto-advance
  useEffect(() => {
    if (!config.autoAdvance || !isActive || !currentStepData) return;

    autoAdvanceTimeoutRef.current = setTimeout(() => {
      if (navigationState.canGoNext) {
        nextStep();
      } else {
        closeTutorial();
      }
    }, config.autoAdvanceDelay);

    return () => {
      if (autoAdvanceTimeoutRef.current) {
        clearTimeout(autoAdvanceTimeoutRef.current);
      }
    };
  }, [currentStep, isActive, config.autoAdvance, config.autoAdvanceDelay]);

  // Keyboard navigation
  useEffect(() => {
    if (!config.keyboardNavigation || !isActive) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case "ArrowRight":
        case "ArrowDown":
        case "Enter":
        case " ":
          event.preventDefault();
          if (navigationState.canGoNext) {
            nextStep();
          }
          break;
        case "ArrowLeft":
        case "ArrowUp":
          event.preventDefault();
          if (navigationState.canGoPrevious) {
            previousStep();
          }
          break;
        case "Escape":
          event.preventDefault();
          closeTutorial();
          break;
        case "s":
        case "S":
          if (config.allowSkip) {
            event.preventDefault();
            skipTutorial();
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isActive, config.keyboardNavigation, config.allowSkip, navigationState]);

  const startTutorial = useCallback(() => {
    if (steps.length === 0) return;
    setIsActive(true);
    setCurrentStep(0);
  }, [steps.length]);

  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1) {
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      onStepChange?.(newStep, steps[newStep]);

      // Clear auto-advance timeout
      if (autoAdvanceTimeoutRef.current) {
        clearTimeout(autoAdvanceTimeoutRef.current);
      }
    } else {
      // Tutorial completed
      closeTutorial();
      onComplete?.();
    }
  }, [currentStep, steps, onStepChange, onComplete]);

  const previousStep = useCallback(() => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      onStepChange?.(newStep, steps[newStep]);

      // Clear auto-advance timeout
      if (autoAdvanceTimeoutRef.current) {
        clearTimeout(autoAdvanceTimeoutRef.current);
      }
    }
  }, [currentStep, steps, onStepChange]);

  const goToStep = useCallback(
    (step: number) => {
      if (step >= 0 && step < steps.length) {
        setCurrentStep(step);
        onStepChange?.(step, steps[step]);

        // Clear auto-advance timeout
        if (autoAdvanceTimeoutRef.current) {
          clearTimeout(autoAdvanceTimeoutRef.current);
        }
      }
    },
    [steps, onStepChange],
  );

  const skipTutorial = useCallback(() => {
    closeTutorial();
    onComplete?.();
  }, [onComplete]);

  const closeTutorial = useCallback(() => {
    setIsActive(false);
    setCurrentStep(-1);
    setHighlightedElement(null);
    setTooltipPosition(null);

    // Clear auto-advance timeout
    if (autoAdvanceTimeoutRef.current) {
      clearTimeout(autoAdvanceTimeoutRef.current);
    }
  }, []);

  return {
    currentStep,
    currentStepData,
    highlightedElement,
    tooltipPosition,
    navigationState,
    isActive,
    nextStep,
    previousStep,
    goToStep,
    skipTutorial,
    closeTutorial,
    startTutorial,
  };
}
