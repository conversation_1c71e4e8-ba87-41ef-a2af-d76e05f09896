"use client";

import React, { useState } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { useSession } from "next-auth/react";
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON><PERSON>, Plus, Trash, Save, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CreateTutorialData, TutorialStep } from "@/models/Tutorial";
import { TOAST_CONFIG, ADMIN_CONFIG } from "@/lib/constants";

const tutorialFormSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").max(100),
  description: z.string().optional(),
  language: z.string().default("en"),
  metadata: z.object({
    category: z.string().min(1, "Category is required"),
    difficulty: z.enum(["beginner", "intermediate", "advanced"]),
    estimatedTime: z.coerce.number().min(1, "Estimated time is required"),
    tags: z.array(z.string()).default([]),
    targetUrl: z.string().url().optional().or(z.literal("")),
    version: z.string().default("1.0.0"),
  }),
  steps: z.array(
    z.object({
      title: z.string().min(1, "Step title is required"),
      description: z.string().min(1, "Step description is required"),
      action: z.enum([
        "click",
        "hover",
        "type",
        "scroll",
        "wait",
        "explain",
      ]),
      content: z.string().min(1, "Step content is required"),
      voiceContent: z.string().optional(),
      selector: z.string().optional(),
      position: z.object({
        x: z.number(),
        y: z.number(),
      }),
      duration: z.number().optional(),
      conditions: z
        .object({
          url: z.string().optional(),
          element: z.string().optional(),
          text: z.string().optional(),
        })
        .optional(),
    })
  ).min(1, "At least one step is required"),
});

type TutorialFormValues = z.infer<typeof tutorialFormSchema>;

const defaultStep: Omit<TutorialStep, "id"> = {
  title: "",
  description: "",
  action: "explain",
  content: "",
  position: { x: 0, y: 0 },
};

export default function CreateTutorialPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const form = useForm<TutorialFormValues>({
    resolver: zodResolver(tutorialFormSchema),
    defaultValues: {
      title: "",
      description: "",
      language: "en",
      metadata: {
        category: "",
        difficulty: "beginner",
        estimatedTime: 5,
        tags: [],
        targetUrl: "",
        version: "1.0.0",
      },
      steps: [defaultStep],
    },
  });

  React.useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    } else if (
      status === "authenticated" &&
      !ADMIN_CONFIG.permissions.allowedRoles.includes(
        (session?.user as any)?.role
      )
    ) {
      router.push("/dashboard");
    }
  }, [status, session, router]);

  const onSubmit = async (data: TutorialFormValues) => {
    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch("/api/tutorials", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create tutorial");
      }

      toast({
        title: "Tutorial Created",
        description: `Tutorial "${data.title}" has been created successfully.`,
        variant: TOAST_CONFIG.variants.success,
      });

      router.push("/admin/tutorials");
    } catch (error) {
      console.error("Error creating tutorial:", error);
      setError(error instanceof Error ? error.message : "An unexpected error occurred");
      toast({
        title: "Error",
        description: "Failed to create tutorial. Please try again.",
        variant: TOAST_CONFIG.variants.error,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const addStep = () => {
    const currentSteps = form.getValues("steps");
    form.setValue("steps", [...currentSteps, { ...defaultStep }]);
  };

  const removeStep = (index: number) => {
    const currentSteps = form.getValues("steps");
    if (currentSteps.length > 1) {
      form.setValue(
        "steps",
        currentSteps.filter((_, i) => i !== index)
      );
    }
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <AdminLayout
      title="Create Tutorial"
      description="Create a new interactive tutorial for users."
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => router.push("/admin/tutorials")}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Tutorials
          </Button>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle>Tutorial Details</CardTitle>
                <CardDescription>
                  Basic information about the tutorial
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter tutorial title" {...field} />
                      </FormControl>
                      <FormDescription>
                        A clear and concise title for the tutorial
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter tutorial description"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        A brief description of what users will learn
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="metadata.category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="navigation">Navigation</SelectItem>
                              <SelectItem value="forms">Forms</SelectItem>
                              <SelectItem value="ecommerce">E-commerce</SelectItem>
                              <SelectItem value="productivity">Productivity</SelectItem>
                              <SelectItem value="accessibility">Accessibility</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="metadata.difficulty"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Difficulty</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select difficulty" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">Intermediate</SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="metadata.estimatedTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated Time (minutes)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="metadata.targetUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Target URL (optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The website where this tutorial will be applied
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Tutorial Steps</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addStep}
                    className="gap-1"
                  >
                    <Plus className="h-4 w-4" /> Add Step
                  </Button>
                </CardTitle>
                <CardDescription>
                  Define the steps users will follow in this tutorial
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {form.watch("steps").map((step, index) => (
                  <div key={index} className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">Step {index + 1}</h3>
                      {form.watch("steps").length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeStep(index)}
                          className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name={`steps.${index}.title`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Step Title</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter step title"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`steps.${index}.action`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Action Type</FormLabel>
                            <FormControl>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select action" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="explain">Explain</SelectItem>
                                  <SelectItem value="click">Click</SelectItem>
                                  <SelectItem value="hover">Hover</SelectItem>
                                  <SelectItem value="type">Type</SelectItem>
                                  <SelectItem value="scroll">Scroll</SelectItem>
                                  <SelectItem value="wait">Wait</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`steps.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Step Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe what happens in this step"
                              className="min-h-[80px]"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`steps.${index}.content`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Content</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="The main content for this step"
                              className="min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            The main content to display during this step
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {form.watch(`steps.${index}.action`) !== "explain" && (
                      <FormField
                        control={form.control}
                        name={`steps.${index}.selector`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Element Selector (optional)</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="CSS selector (e.g., #button-id, .class-name)"
                                {...field}
                                value={field.value || ""}
                              />
                            </FormControl>
                            <FormDescription>
                              CSS selector for the target element
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {index < form.watch("steps").length - 1 && (
                      <Separator className="my-6" />
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end">
              <Button
                type="submit"
                className="gap-2"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-background"></div>
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    <span>Create Tutorial</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </AdminLayout>
  );
} 