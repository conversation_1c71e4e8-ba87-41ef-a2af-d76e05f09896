/* TutorAI Content Script Styles */

/* Element Highlighting */
.tutorai-highlighted {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  z-index: 999999 !important;
}

.tutorai-highlighted:hover {
  outline-color: #1d4ed8 !important;
  background-color: rgba(59, 130, 246, 0.2) !important;
  transform: scale(1.02) !important;
}

.tutorai-ai-highlighted {
  outline: 3px solid var(--tutorai-highlight-color, #10b981) !important;
  outline-offset: 3px !important;
  background-color: rgba(16, 185, 129, 0.15) !important;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
  animation: tutorai-pulse 2s infinite !important;
  position: relative !important;
  z-index: 999999 !important;
}

@keyframes tutorai-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
  }
}

/* Tutorial Overlay */
#tutorai-overlay {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  width: 380px !important;
  max-height: 500px !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #e5e7eb !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: #1f2937 !important;
  z-index: 2147483647 !important;
  overflow: hidden !important;
  animation: tutorai-slide-in 0.3s ease-out !important;
}

@keyframes tutorai-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.tutorai-overlay-content {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

/* Header */
.tutorai-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  color: white !important;
  padding: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  border-radius: 12px 12px 0 0 !important;
}

.tutorai-header img {
  width: 24px !important;
  height: 24px !important;
  border-radius: 4px !important;
}

.tutorai-header span {
  font-weight: 600 !important;
  font-size: 16px !important;
  flex: 1 !important;
}

#tutorai-close {
  background: rgba(255, 255, 255, 0.2) !important;
  border: none !important;
  color: white !important;
  width: 28px !important;
  height: 28px !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  font-size: 18px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
}

#tutorai-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* Body */
.tutorai-body {
  padding: 20px !important;
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: 400px !important;
}

#tutorai-status {
  background: #f0f9ff !important;
  color: #0c4a6e !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  margin-bottom: 16px !important;
  border: 1px solid #bae6fd !important;
}

#tutorai-response {
  background: #f9fafb !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin-bottom: 16px !important;
  min-height: 80px !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  line-height: 1.5 !important;
  white-space: pre-wrap !important;
}

#tutorai-response:empty::before {
  content: "AI response will appear here..." !important;
  color: #9ca3af !important;
  font-style: italic !important;
}

/* Controls */
.tutorai-controls {
  display: flex !important;
  gap: 8px !important;
  flex-wrap: wrap !important;
}

.tutorai-controls button {
  background: #3b82f6 !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  flex: 1 !important;
  min-width: 120px !important;
}

.tutorai-controls button:hover {
  background: #2563eb !important;
  transform: translateY(-1px) !important;
}

.tutorai-controls button:active {
  transform: translateY(0) !important;
}

/* Responsive Design */
@media (max-width: 480px) {
  #tutorai-overlay {
    top: 10px !important;
    right: 10px !important;
    left: 10px !important;
    width: auto !important;
  }
}

/* Scrollbar for overlay content */
.tutorai-body::-webkit-scrollbar,
#tutorai-response::-webkit-scrollbar {
  width: 6px !important;
}

.tutorai-body::-webkit-scrollbar-track,
#tutorai-response::-webkit-scrollbar-track {
  background: #f1f5f9 !important;
}

.tutorai-body::-webkit-scrollbar-thumb,
#tutorai-response::-webkit-scrollbar-thumb {
  background: #cbd5e1 !important;
  border-radius: 3px !important;
}

.tutorai-body::-webkit-scrollbar-thumb:hover,
#tutorai-response::-webkit-scrollbar-thumb:hover {
  background: #94a3b8 !important;
}

/* Prevent conflicts with website styles */
#tutorai-overlay * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
}

#tutorai-overlay button,
#tutorai-overlay input,
#tutorai-overlay select,
#tutorai-overlay textarea {
  font-family: inherit !important;
}

/* Loading animation for response */
.tutorai-loading {
  position: relative !important;
}

.tutorai-loading::after {
  content: "" !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  width: 20px !important;
  height: 20px !important;
  margin: -10px 0 0 -10px !important;
  border: 2px solid #e5e7eb !important;
  border-top: 2px solid #3b82f6 !important;
  border-radius: 50% !important;
  animation: tutorai-spin 1s linear infinite !important;
}

@keyframes tutorai-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
