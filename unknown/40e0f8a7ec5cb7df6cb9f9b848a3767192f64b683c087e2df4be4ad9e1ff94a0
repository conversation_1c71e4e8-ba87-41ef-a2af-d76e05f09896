import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get recent activities
    const activities = await prisma.analytics.findMany({
      where: {
        userId: user.id,
        action: {
          in: [
            "tutorial_started",
            "tutorial_completed",
            "ai_explanation_requested",
            "voice_used",
          ],
        },
      },
      orderBy: {
        timestamp: "desc",
      },
      take: 10,
      select: {
        id: true,
        action: true,
        metadata: true,
        timestamp: true,
      },
    });

    return NextResponse.json({
      activities,
    });
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
