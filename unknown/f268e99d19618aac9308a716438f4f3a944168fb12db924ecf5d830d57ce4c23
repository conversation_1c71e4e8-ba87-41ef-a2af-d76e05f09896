# TutorAI - AI-Powered Browser Extension Tutoring System

Real-time AI explanations, voice synthesis, and interactive guidance for any webpage. Transform how you learn and navigate the web with intelligent assistance.

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- PostgreSQL database

### Environment Setup

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Configure your database connection using one of two methods:

   **Option 1:** Set the full DATABASE_URL:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/tutorial_db?schema=public"
   ```

   **Option 2:** Set individual components:
   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_USER=postgres
   DB_PASSWORD=postgres
   DB_NAME=tutorial_db
   DB_SCHEMA=public
   ```

3. Set up your NextAuth configuration:
```
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-generated-secret-key"
```

4. (Optional) Configure Google OAuth:
```
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

5. Configure application settings:
```
NODE_ENV=development
PORT=3000
API_URL=http://localhost:3000/api
```

### Installation

```bash
# Install dependencies
npm install

# Set up the database
npm run db:setup

# Initialize the database with required data
npm run db:init

# Run the development server
npm run dev
```

## Database Setup

The application uses PostgreSQL with Prisma ORM. Make sure your PostgreSQL server is running and accessible with the credentials provided in your `.env` file.

To initialize your database:

```bash
# Generate Prisma client
npx prisma generate

# Create database tables
npx prisma migrate deploy

# Initialize the database with required data
npm run db:init

# Check database connection
npm run db:check
```

## Troubleshooting

### Database Connection Issues

If you encounter the error: `PrismaClientInitializationError: Environment variable not found: DATABASE_URL`, make sure:

1. Your `.env` file exists in the project root directory
2. Either DATABASE_URL or the individual DB_* variables are correctly set in the `.env` file
3. Your PostgreSQL server is running and accessible
4. The database specified in the URL exists

You can check your database connection with:

```bash
npm run db:check
```

### Common Database URL Format

The DATABASE_URL should follow this format:

```
DATABASE_URL="postgresql://username:password@hostname:port/database_name?schema=public"
```

Example:
```
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/tutorial_db?schema=public"
```

### Setting Up PostgreSQL

If you don't have PostgreSQL installed:

#### Using Docker (Recommended)

```bash
# Start PostgreSQL container
docker run --name tutorial-postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_USER=postgres -e POSTGRES_DB=tutorial_db -p 5432:5432 -d postgres

# Check if container is running
docker ps
```

#### Local Installation

- [PostgreSQL Downloads](https://www.postgresql.org/download/)
- After installation, create a database:
  ```sql
  CREATE DATABASE tutorial_db;
  ```

### Reset Database

If you need to reset your database:

```bash
npm run db:reset
```

This will reset the database and reapply all migrations.
