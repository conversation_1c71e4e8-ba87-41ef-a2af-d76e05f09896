import { z } from "zod";

// Authentication validation schemas
export const signInSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .toLowerCase()
    .trim(),
  password: z
    .string()
    .min(1, "Password is required")
    .min(8, "Password must be at least 8 characters"),
});

export const signUpSchema = z
  .object({
    name: z
      .string()
      .min(1, "Full name is required")
      .min(2, "Name must be at least 2 characters")
      .max(50, "Name must be less than 50 characters")
      .regex(
        /^[a-zA-Z\s'-]+$/,
        "Name can only contain letters, spaces, hyphens, and apostrophes",
      )
      .trim(),
    email: z
      .string()
      .min(1, "Email is required")
      .email("Please enter a valid email address")
      .toLowerCase()
      .trim(),
    password: z
      .string()
      .min(1, "Password is required")
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),
    acceptTerms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .toLowerCase()
    .trim(),
});

// User management validation schemas
export const createUserSchema = z.object({
  name: z
    .string()
    .min(1, "Full name is required")
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(
      /^[a-zA-Z\s'-]+$/,
      "Name can only contain letters, spaces, hyphens, and apostrophes",
    )
    .trim(),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .toLowerCase()
    .trim(),
  role: z.enum(["USER", "MODERATOR", "ADMIN"]),
  isActive: z.boolean().default(true),
});

export const updateUserSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(
      /^[a-zA-Z\s'-]+$/,
      "Name can only contain letters, spaces, hyphens, and apostrophes",
    )
    .trim()
    .optional(),
  role: z.enum(["USER", "MODERATOR", "ADMIN"]).optional(),
  isActive: z.boolean().optional(),
  subscription: z.string().optional(),
});

export const updatePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .max(128, "Password must be less than 128 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      ),
    confirmPassword: z.string().min(1, "Please confirm your password"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export const updateProfileSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(
      /^[a-zA-Z\s'-]+$/,
      "Name can only contain letters, spaces, hyphens, and apostrophes",
    )
    .trim(),
  settings: z
    .object({
      language: z.string().default("en"),
      voiceProvider: z.string().default("elevenlabs"),
      voiceSettings: z
        .object({
          speed: z.number().min(0.5).max(2.0).default(1.0),
          pitch: z.number().min(0.5).max(2.0).default(1.0),
          voice: z.string().default("default"),
        })
        .default({
          speed: 1.0,
          pitch: 1.0,
          voice: "default",
        }),
      aiProvider: z.string().default("openai"),
      tutorialPreferences: z
        .object({
          autoStart: z.boolean().default(true),
          voiceCommands: z.boolean().default(true),
          difficulty: z
            .enum(["beginner", "intermediate", "advanced"])
            .default("beginner"),
        })
        .default({
          autoStart: true,
          voiceCommands: true,
          difficulty: "beginner",
        }),
      accessibility: z
        .object({
          highContrast: z.boolean().default(false),
          largeText: z.boolean().default(false),
          screenReader: z.boolean().default(false),
        })
        .default({
          highContrast: false,
          largeText: false,
          screenReader: false,
        }),
    })
    .optional(),
});

export const assignPermissionSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  permission: z.enum([
    "USER_READ",
    "USER_WRITE",
    "USER_DELETE",
    "TUTORIAL_READ",
    "TUTORIAL_WRITE",
    "TUTORIAL_DELETE",
    "TUTORIAL_PUBLISH",
    "ANALYTICS_READ",
    "ANALYTICS_EXPORT",
    "AI_USAGE_READ",
    "AI_USAGE_MANAGE",
    "SYSTEM_CONFIG",
    "AUDIT_LOG_READ",
    "BILLING_READ",
    "BILLING_WRITE",
  ]),
  expiresAt: z.date().optional(),
});

// Type exports
export type SignInFormData = z.infer<typeof signInSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type CreateUserFormData = z.infer<typeof createUserSchema>;
export type UpdateUserFormData = z.infer<typeof updateUserSchema>;
export type UpdatePasswordFormData = z.infer<typeof updatePasswordSchema>;
export type UpdateProfileFormData = z.infer<typeof updateProfileSchema>;
export type AssignPermissionFormData = z.infer<typeof assignPermissionSchema>;
