import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database";
import { APP_CONFIG } from "@/lib/config";

/**
 * Health check endpoint for monitoring
 * GET /api/health
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Check database connection
    const dbStart = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const dbResponseTime = Date.now() - dbStart;

    // Get basic system info
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();

    // Check if critical services are available
    const checks = {
      database: {
        status: "healthy",
        responseTime: dbResponseTime,
      },
      memory: {
        status:
          memoryUsage.heapUsed < memoryUsage.heapTotal * 0.9
            ? "healthy"
            : "warning",
        usage: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          percentage: Math.round(
            (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
          ),
        },
      },
    };

    const overallStatus = Object.values(checks).every(
      (check) => check.status === "healthy",
    )
      ? "healthy"
      : "degraded";

    const responseTime = Date.now() - startTime;

    return NextResponse.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: Math.round(uptime),
      responseTime,
      version: process.env.npm_package_version || "1.0.0",
      environment: APP_CONFIG.NODE_ENV,
      checks,
    });
  } catch (error) {
    console.error("Health check failed:", error);

    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
        responseTime: Date.now() - startTime,
      },
      { status: 503 },
    );
  }
}

/**
 * Detailed health check for internal monitoring
 * GET /api/health?detailed=true
 */
export async function HEAD(request: NextRequest) {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
