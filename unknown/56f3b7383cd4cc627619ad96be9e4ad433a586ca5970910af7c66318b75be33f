"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  BarChart3,
  Users,
  Shield,
  BookOpen,
  TrendingUp,
  Settings,
  Menu,
  X,
  Sparkles,
  ChevronRight,
} from "lucide-react";
import { ADMIN_CONFIG } from "@/lib/constants";
import { cn } from "@/lib/utils";

interface AdminLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
}

export default function AdminLayout({
  children,
  title,
  description,
}: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const pathname = usePathname();

  const navigationItems = [
    {
      id: "overview",
      label: "Overview",
      href: "/admin",
      icon: BarChart3,
    },
    {
      id: "users",
      label: "Users",
      href: "/admin/user",
      icon: Users,
    },
    {
      id: "roles",
      label: "Roles & Permissions",
      href: "/admin/roles",
      icon: Shield,
    },
    {
      id: "tutorials",
      label: "Tutorials",
      href: "/admin/tutorials",
      icon: BookOpen,
    },
    {
      id: "analytics",
      label: "Analytics",
      href: "/admin/analytics",
      icon: TrendingUp,
    },
    {
      id: "settings",
      label: "Settings",
      href: "/admin/settings",
      icon: Settings,
    },
  ];

  const isActive = (href: string) => {
    if (href === "/admin") {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar */}
      <aside
        className={cn(
          "fixed inset-y-0 left-0 z-50 flex flex-col border-r bg-background transition-all duration-300",
          sidebarOpen
            ? `w-[${ADMIN_CONFIG.sidebar.width.expanded}px]`
            : `w-[${ADMIN_CONFIG.sidebar.width.collapsed}px]`
        )}
      >
        {/* Sidebar Header */}
        <div className="h-16 flex items-center justify-between px-4 border-b">
          <Link
            href="/admin"
            className={cn(
              "flex items-center gap-2",
              !sidebarOpen && "justify-center"
            )}
          >
            <Sparkles className="h-6 w-6 text-primary" />
            {sidebarOpen && <span className="font-bold text-lg">Admin</span>}
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            {sidebarOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 py-6 px-2 space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link key={item.id} href={item.href}>
                <Button
                  variant={isActive(item.href) ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start mb-1",
                    !sidebarOpen && "justify-center"
                  )}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {sidebarOpen && <span>{item.label}</span>}
                </Button>
              </Link>
            );
          })}
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t">
          <Link href="/dashboard">
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start",
                !sidebarOpen && "justify-center"
              )}
            >
              <ChevronRight className="h-4 w-4 mr-2" />
              {sidebarOpen && <span>Back to App</span>}
            </Button>
          </Link>
        </div>
      </aside>

      {/* Main Content */}
      <main
        className={cn(
          "flex-1 transition-all duration-300",
          sidebarOpen
            ? `ml-[${ADMIN_CONFIG.sidebar.width.expanded}px]`
            : `ml-[${ADMIN_CONFIG.sidebar.width.collapsed}px]`
        )}
      >
        {/* Header */}
        <header className="sticky top-0 z-40 h-16 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container flex h-full items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
              {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
              )}
            </div>
            <ThemeSwitcher />
          </div>
        </header>

        {/* Page Content */}
        <div className="container py-8">{children}</div>
      </main>
    </div>
  );
}
