import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { prisma } from "@/lib/database";
import { canUserPerformAction, logAuditEvent } from "@/lib/permissions";
import { updatePasswordSchema } from "@/lib/validation";
import bcrypt from "bcryptjs";

// PUT /api/admin/users/[id]/password - Update user password
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 },
      );
    }

    // Check permissions
    const canWrite = await canUserPerformAction(
      (session.user as any).id,
      "USER_WRITE",
    );
    if (!canWrite) {
      return NextResponse.json(
        { message: "Insufficient permissions" },
        { status: 403 },
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = updatePasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: validationResult.error.flatten().fieldErrors,
        },
        { status: 400 },
      );
    }

    const { currentPassword, newPassword } = validationResult.data;

    // Get current user data
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: { id: true, email: true, password: true },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    // Verify current password if user is updating their own password
    if (params.id === (session.user as any).id) {
      if (!user.password) {
        return NextResponse.json(
          { message: "User has no password set" },
          { status: 400 },
        );
      }

      const isCurrentPasswordValid = await bcrypt.compare(
        currentPassword,
        user.password,
      );

      if (!isCurrentPasswordValid) {
        return NextResponse.json(
          { message: "Current password is incorrect" },
          { status: 400 },
        );
      }
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    await prisma.user.update({
      where: { id: params.id },
      data: {
        password: hashedPassword,
        // loginAttempts: 0, // Reset login attempts
        // lockedUntil: null, // Unlock account if locked
      },
    });

    // Log audit event
    await logAuditEvent({
      userId: (session.user as any).id,
      action: "password_updated",
      resource: "user",
      resourceId: params.id,
      metadata: {
        targetUserId: params.id,
        selfUpdate: params.id === (session.user as any).id,
      },
      ipAddress: request.ip,
      userAgent: request.headers.get("user-agent") || undefined,
    });

    return NextResponse.json({
      message: "Password updated successfully",
    });
  } catch (error) {
    console.error("Error updating password:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 },
    );
  }
}
