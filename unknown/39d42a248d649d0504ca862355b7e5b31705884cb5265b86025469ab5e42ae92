export interface AIUsage {
  id: string;
  userId: string;
  provider: AIProvider;
  model: string;
  tokens: number;
  cost: number;
  timestamp: Date;
  requestType: AIRequestType;
  metadata: AIUsageMetadata;
}

export type AIProvider =
  | "openai"
  | "anthropic"
  | "google"
  | "openrouter"
  | "groq";

export type AIRequestType =
  | "explanation"
  | "tutorial_generation"
  | "chat"
  | "translation"
  | "summarization";

export interface AIUsageMetadata {
  prompt?: string;
  response?: string;
  duration: number;
  success: boolean;
  errorMessage?: string;
  tutorialId?: string;
  language?: string;
  customData?: Record<string, any>;
}

export interface CreateAIUsageData {
  userId: string;
  provider: AIProvider;
  model: string;
  tokens: number;
  cost: number;
  requestType: AIRequestType;
  metadata: AIUsageMetadata;
}

export interface AIProviderConfig {
  provider: AIProvider;
  name: string;
  isActive: boolean;
  models: {
    id: string;
    name: string;
    costPer1kTokens: number;
    maxTokens: number;
  }[];
  apiKey?: string;
  baseUrl?: string;
  settings: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
  };
}

export interface AIUsageReport {
  period: "day" | "week" | "month" | "year";
  totalRequests: number;
  totalTokens: number;
  totalCost: number;
  byProvider: {
    provider: AIProvider;
    requests: number;
    tokens: number;
    cost: number;
  }[];
  byRequestType: {
    type: AIRequestType;
    requests: number;
    tokens: number;
    cost: number;
  }[];
  topUsers: {
    userId: string;
    requests: number;
    cost: number;
  }[];
}
