"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  BarChart3,
  <PERSON><PERSON>hart,
  Pie<PERSON><PERSON>,
  Settings,
  Users,
  BookOpen,
  Zap,
  Activity,
  ChevronRight,
} from "lucide-react";

interface DashboardPreviewProps {
  className?: string;
}

export default function DashboardPreview({
  className = "",
}: DashboardPreviewProps) {
  const [activeTab, setActiveTab] = useState("analytics");

  return (
    <div
      className={`w-full max-w-6xl mx-auto bg-background rounded-xl shadow-lg overflow-hidden border ${className}`}
    >
      {/* Dashboard Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="bg-primary/10 p-2 rounded-md">
            <Zap className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-lg">AI Tutor Dashboard</h3>
            <p className="text-sm text-muted-foreground">
              Manage your tutoring system
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Avatar>
            <AvatarImage
              src="https://api.dicebear.com/7.x/avataaars/svg?seed=admin"
              alt="Admin"
            />
            <AvatarFallback>AD</AvatarFallback>
          </Avatar>
        </div>
      </div>

      {/* Dashboard Navigation */}
      <div className="flex">
        <div className="w-48 border-r p-4 hidden md:block">
          <nav className="space-y-2">
            <Button
              variant={activeTab === "analytics" ? "secondary" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("analytics")}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
            <Button
              variant={activeTab === "tutorials" ? "secondary" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("tutorials")}
            >
              <BookOpen className="h-4 w-4 mr-2" />
              Tutorials
            </Button>
            <Button
              variant={activeTab === "users" ? "secondary" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("users")}
            >
              <Users className="h-4 w-4 mr-2" />
              Users
            </Button>
            <Button
              variant={activeTab === "ai" ? "secondary" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("ai")}
            >
              <Activity className="h-4 w-4 mr-2" />
              AI Providers
            </Button>
          </nav>
        </div>

        {/* Dashboard Content */}
        <div className="flex-1 p-4">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="mb-4 md:hidden">
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="tutorials">Tutorials</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="ai">AI</TabsTrigger>
            </TabsList>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Users
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">2,845</div>
                    <p className="text-xs text-muted-foreground">
                      +12% from last month
                    </p>
                    <div className="mt-4 h-[60px] bg-muted/30 rounded-md flex items-end">
                      {[40, 30, 45, 25, 60, 75, 65].map((height, i) => (
                        <div
                          key={i}
                          className="flex-1 bg-primary mx-[1px] rounded-t-sm"
                          style={{ height: `${height}%` }}
                        />
                      ))}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Active Tutorials
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">128</div>
                    <p className="text-xs text-muted-foreground">
                      +4 new this week
                    </p>
                    <div className="mt-4 h-[60px] bg-muted/30 rounded-md flex items-center justify-center">
                      <PieChart className="h-12 w-12 text-primary/70" />
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      AI Usage
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">14.2K</div>
                    <p className="text-xs text-muted-foreground">
                      Requests this month
                    </p>
                    <div className="mt-4 h-[60px] bg-muted/30 rounded-md flex items-center justify-center">
                      <LineChart className="h-12 w-12 text-primary/70" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Usage Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm">OpenAI Usage</span>
                        <span className="text-sm font-medium">78%</span>
                      </div>
                      <Progress value={78} className="h-2" />
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm">Anthropic Usage</span>
                        <span className="text-sm font-medium">45%</span>
                      </div>
                      <Progress value={45} className="h-2" />
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm">Google AI Usage</span>
                        <span className="text-sm font-medium">23%</span>
                      </div>
                      <Progress value={23} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Tutorials Tab */}
            <TabsContent value="tutorials" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Tutorial Management</CardTitle>
                    <Button size="sm">Create Tutorial</Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      {
                        title: "Getting Started with AI Tutor",
                        steps: 8,
                        active: true,
                      },
                      {
                        title: "Advanced Web Navigation",
                        steps: 12,
                        active: true,
                      },
                      {
                        title: "Form Filling Tutorial",
                        steps: 5,
                        active: false,
                      },
                      {
                        title: "E-commerce Shopping Guide",
                        steps: 10,
                        active: true,
                      },
                    ].map((tutorial, i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between p-3 border rounded-md"
                      >
                        <div>
                          <h4 className="font-medium">{tutorial.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {tutorial.steps} steps
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={tutorial.active ? "default" : "outline"}
                          >
                            {tutorial.active ? "Active" : "Draft"}
                          </Badge>
                          <Button variant="ghost" size="icon">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Users Tab */}
            <TabsContent value="users" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>User Management</CardTitle>
                    <Button size="sm">Add User</Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      {
                        name: "Alex Johnson",
                        email: "<EMAIL>",
                        role: "Admin",
                      },
                      {
                        name: "Sarah Williams",
                        email: "<EMAIL>",
                        role: "Editor",
                      },
                      {
                        name: "Michael Brown",
                        email: "<EMAIL>",
                        role: "Viewer",
                      },
                      {
                        name: "Emily Davis",
                        email: "<EMAIL>",
                        role: "Editor",
                      },
                    ].map((user, i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between p-3 border rounded-md"
                      >
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarImage
                              src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.name}`}
                              alt={user.name}
                            />
                            <AvatarFallback>
                              {user.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-medium">{user.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              {user.email}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{user.role}</Badge>
                          <Button variant="ghost" size="icon">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* AI Providers Tab */}
            <TabsContent value="ai" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>AI Provider Settings</CardTitle>
                    <Button size="sm">Add Provider</Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: "OpenAI", model: "GPT-4", status: "Active" },
                      {
                        name: "Anthropic",
                        model: "Claude 3",
                        status: "Active",
                      },
                      {
                        name: "Google AI",
                        model: "Gemini Pro",
                        status: "Inactive",
                      },
                    ].map((provider, i) => (
                      <div
                        key={i}
                        className="flex items-center justify-between p-3 border rounded-md"
                      >
                        <div>
                          <h4 className="font-medium">{provider.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Model: {provider.model}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge
                            variant={
                              provider.status === "Active"
                                ? "default"
                                : "outline"
                            }
                          >
                            {provider.status}
                          </Badge>
                          <Button variant="ghost" size="icon">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
