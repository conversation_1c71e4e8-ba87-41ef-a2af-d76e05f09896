import { EMAIL_CONFIG } from "@/lib/config";

/**
 * Email service for sending transactional emails
 * In production, integrate with services like SendGrid, AWS SES, or similar
 */

interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

/**
 * Send an email using the configured SMTP service
 */
export async function sendEmail({
  to,
  subject,
  html,
  text,
}: EmailOptions): Promise<void> {
  // In development, log the email instead of sending
  if (process.env.NODE_ENV === "development") {
    console.log("📧 Email would be sent:");
    console.log(`To: ${to}`);
    console.log(`Subject: ${subject}`);
    console.log(`HTML: ${html}`);
    return;
  }

  // Check if email configuration is available
  if (
    !EMAIL_CONFIG.SMTP_HOST ||
    !EMAIL_CONFIG.SMTP_USER ||
    !EMAIL_CONFIG.SMTP_PASSWORD
  ) {
    console.warn("Email configuration not complete. Email not sent.");
    return;
  }

  // In production, implement actual email sending
  // Example with nodemailer:
  /*
  const nodemailer = require('nodemailer');
  
  const transporter = nodemailer.createTransporter({
    host: EMAIL_CONFIG.SMTP_HOST,
    port: EMAIL_CONFIG.SMTP_PORT,
    secure: EMAIL_CONFIG.SMTP_PORT === 465,
    auth: {
      user: EMAIL_CONFIG.SMTP_USER,
      pass: EMAIL_CONFIG.SMTP_PASSWORD,
    },
  });

  await transporter.sendMail({
    from: EMAIL_CONFIG.SMTP_FROM,
    to,
    subject,
    html,
    text,
  });
  */

  console.log(`Email sent to ${to}: ${subject}`);
}

/**
 * Send password reset email
 */
export async function sendPasswordResetEmail(
  email: string,
  name: string,
  resetLink: string,
): Promise<void> {
  const subject = "Reset Your TutorAI Password";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .button { 
          display: inline-block; 
          background: #6366f1; 
          color: white; 
          padding: 12px 30px; 
          text-decoration: none; 
          border-radius: 5px; 
          margin: 20px 0;
        }
        .footer { 
          background: #f8f9fa; 
          padding: 20px; 
          text-align: center; 
          font-size: 14px; 
          color: #666;
        }
        .warning { 
          background: #fff3cd; 
          border: 1px solid #ffeaa7; 
          padding: 15px; 
          border-radius: 5px; 
          margin: 20px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🧠 TutorAI</h1>
        </div>
        
        <div class="content">
          <h2>Reset Your Password</h2>
          <p>Hello ${name},</p>
          
          <p>We received a request to reset your password for your TutorAI account. If you made this request, click the button below to reset your password:</p>
          
          <div style="text-align: center;">
            <a href="${resetLink}" class="button">Reset Password</a>
          </div>
          
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;">
            ${resetLink}
          </p>
          
          <div class="warning">
            <strong>⚠️ Security Notice:</strong>
            <ul>
              <li>This link will expire in 1 hour for security reasons</li>
              <li>If you didn't request this reset, please ignore this email</li>
              <li>Never share this link with anyone</li>
            </ul>
          </div>
          
          <p>If you're having trouble with the button above, copy and paste the URL into your web browser.</p>
          
          <p>Best regards,<br>The TutorAI Team</p>
        </div>
        
        <div class="footer">
          <p>This email was sent to ${email}. If you didn't request a password reset, you can safely ignore this email.</p>
          <p>© 2024 TutorAI. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Reset Your TutorAI Password
    
    Hello ${name},
    
    We received a request to reset your password for your TutorAI account.
    
    Click this link to reset your password: ${resetLink}
    
    This link will expire in 1 hour for security reasons.
    
    If you didn't request this reset, please ignore this email.
    
    Best regards,
    The TutorAI Team
  `;

  await sendEmail({
    to: email,
    subject,
    html,
    text,
  });
}

/**
 * Send welcome email to new users
 */
export async function sendWelcomeEmail(
  email: string,
  name: string,
): Promise<void> {
  const subject = "Welcome to TutorAI! 🎉";

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to TutorAI</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #6366f1; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .button { 
          display: inline-block; 
          background: #6366f1; 
          color: white; 
          padding: 12px 30px; 
          text-decoration: none; 
          border-radius: 5px; 
          margin: 20px 0;
        }
        .feature { 
          background: #f8f9fa; 
          padding: 15px; 
          margin: 10px 0; 
          border-radius: 5px; 
          border-left: 4px solid #6366f1;
        }
        .footer { 
          background: #f8f9fa; 
          padding: 20px; 
          text-align: center; 
          font-size: 14px; 
          color: #666;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🧠 Welcome to TutorAI!</h1>
        </div>
        
        <div class="content">
          <h2>Hello ${name}! 👋</h2>
          
          <p>Welcome to TutorAI - your AI-powered learning companion! We're excited to have you join our community of learners.</p>
          
          <div style="text-align: center;">
            <a href="${process.env.NEXTAUTH_URL}/dashboard" class="button">Get Started</a>
          </div>
          
          <h3>What you can do with TutorAI:</h3>
          
          <div class="feature">
            <strong>🤖 AI-Powered Explanations</strong><br>
            Get intelligent explanations for any webpage content
          </div>
          
          <div class="feature">
            <strong>🎯 Interactive Tutorials</strong><br>
            Follow step-by-step guided tutorials
          </div>
          
          <div class="feature">
            <strong>🗣️ Voice Guidance</strong><br>
            Listen to explanations with our text-to-speech feature
          </div>
          
          <div class="feature">
            <strong>📊 Progress Tracking</strong><br>
            Monitor your learning progress and achievements
          </div>
          
          <p>Ready to start your learning journey? Click the button above to access your dashboard!</p>
          
          <p>If you have any questions, feel free to reach out to our support team.</p>
          
          <p>Happy learning!<br>The TutorAI Team</p>
        </div>
        
        <div class="footer">
          <p>© 2024 TutorAI. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
    Welcome to TutorAI!
    
    Hello ${name}!
    
    Welcome to TutorAI - your AI-powered learning companion!
    
    What you can do:
    - Get AI-powered explanations for any webpage
    - Follow interactive tutorials
    - Listen to voice guidance
    - Track your learning progress
    
    Get started: ${process.env.NEXTAUTH_URL}/dashboard
    
    Happy learning!
    The TutorAI Team
  `;

  await sendEmail({
    to: email,
    subject,
    html,
    text,
  });
}
