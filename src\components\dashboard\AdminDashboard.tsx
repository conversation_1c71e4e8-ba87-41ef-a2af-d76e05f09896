"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  BarChart3,
  Users,
  BookOpen,
  Zap,
  TrendingUp,
  Settings,
  Shield,
  Globe,
  Activity,
  Clock,
  Target,
  DollarSign,
} from "lucide-react";

interface AdminDashboardProps {
  className?: string;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ className = "" }) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [dashboardData, setDashboardData] = useState({
    totalUsers: 12847,
    activeUsers: 8934,
    totalTutorials: 156,
    completionRate: 78.5,
    averageSessionTime: 24,
    revenue: 45670,
    topTutorials: [
      { id: "1", title: "Getting Started with AI Tutor", views: 2847, completions: 2234 },
      { id: "2", title: "Advanced Web Navigation", views: 1923, completions: 1456 },
      { id: "3", title: "Voice Commands Tutorial", views: 1654, completions: 1289 },
      { id: "4", title: "Form Filling Automation", views: 1432, completions: 1098 },
      { id: "5", title: "E-commerce Navigation", views: 1287, completions: 967 },
    ],
    recentUsers: [
      { id: "1", name: "John Doe", email: "<EMAIL>", joinDate: "2024-01-15", status: "active" },
      { id: "2", name: "Jane Smith", email: "<EMAIL>", joinDate: "2024-01-14", status: "active" },
      { id: "3", name: "Mike Johnson", email: "<EMAIL>", joinDate: "2024-01-13", status: "inactive" },
      { id: "4", name: "Sarah Wilson", email: "<EMAIL>", joinDate: "2024-01-12", status: "active" },
      { id: "5", name: "David Brown", email: "<EMAIL>", joinDate: "2024-01-11", status: "active" },
    ],
    aiProviderUsage: [
      { provider: "OpenAI", usage: 65, cost: 1234.56 },
      { provider: "Anthropic", usage: 25, cost: 567.89 },
      { provider: "Google AI", usage: 10, cost: 234.12 },
    ],
  });

  return (
    <div className={`w-full bg-background ${className}`}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor system performance, manage users, and track analytics.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.totalUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.activeUsers.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+8%</span> from last week
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.completionRate}%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2.1%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${dashboardData.revenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+15%</span> from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Dashboard Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="tutorials">Tutorials</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Tutorials */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Tutorials</CardTitle>
                  <CardDescription>
                    Most popular tutorials by completion rate
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.topTutorials.map((tutorial, index) => (
                      <div key={tutorial.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-medium text-sm">
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium text-sm">{tutorial.title}</p>
                            <p className="text-xs text-muted-foreground">
                              {tutorial.views} views • {tutorial.completions} completions
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline">
                          {Math.round((tutorial.completions / tutorial.views) * 100)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* AI Provider Usage */}
              <Card>
                <CardHeader>
                  <CardTitle>AI Provider Usage</CardTitle>
                  <CardDescription>
                    Distribution and costs by provider
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {dashboardData.aiProviderUsage.map((provider) => (
                      <div key={provider.provider} className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium">{provider.provider}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-muted-foreground">{provider.usage}%</span>
                            <span className="font-medium">${provider.cost}</span>
                          </div>
                        </div>
                        <Progress value={provider.usage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* System Health */}
            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
                <CardDescription>
                  Real-time system performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">99.9%</div>
                    <p className="text-sm text-muted-foreground">Uptime</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">145ms</div>
                    <p className="text-sm text-muted-foreground">Avg Response</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">2.3M</div>
                    <p className="text-sm text-muted-foreground">API Calls</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">0.02%</div>
                    <p className="text-sm text-muted-foreground">Error Rate</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Users</CardTitle>
                <CardDescription>
                  Latest user registrations and activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Join Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dashboardData.recentUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{user.name}</span>
                        </TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{user.joinDate}</TableCell>
                        <TableCell>
                          <Badge variant={user.status === 'active' ? 'default' : 'secondary'}>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm">
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tutorials" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Tutorial Management</CardTitle>
                <CardDescription>
                  Create, edit, and manage interactive tutorials
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Active Tutorials</h3>
                      <p className="text-sm text-muted-foreground">
                        {dashboardData.totalTutorials} tutorials currently active
                      </p>
                    </div>
                    <Button>Create Tutorial</Button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center space-y-2">
                          <BookOpen className="h-8 w-8 mx-auto text-blue-500" />
                          <div className="text-2xl font-bold">156</div>
                          <p className="text-sm text-muted-foreground">Total Tutorials</p>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center space-y-2">
                          <TrendingUp className="h-8 w-8 mx-auto text-green-500" />
                          <div className="text-2xl font-bold">89%</div>
                          <p className="text-sm text-muted-foreground">Avg Completion</p>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center space-y-2">
                          <Clock className="h-8 w-8 mx-auto text-purple-500" />
                          <div className="text-2xl font-bold">12m</div>
                          <p className="text-sm text-muted-foreground">Avg Duration</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Analytics</CardTitle>
                <CardDescription>
                  Detailed insights and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] flex items-center justify-center text-muted-foreground">
                  <div className="text-center space-y-4">
                    <BarChart3 className="h-16 w-16 mx-auto" />
                    <div>
                      <h3 className="text-lg font-medium">Analytics Dashboard</h3>
                      <p>Comprehensive charts and metrics would be displayed here</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Configuration</CardTitle>
                  <CardDescription>
                    Manage system-wide settings and preferences
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">AI Providers</h4>
                    <div className="grid grid-cols-2 gap-2">
                      <Button variant="outline" size="sm">OpenAI</Button>
                      <Button variant="outline" size="sm">Anthropic</Button>
                      <Button variant="outline" size="sm">Google AI</Button>
                      <Button variant="outline" size="sm">Configure</Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">Voice Settings</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">ElevenLabs Integration</span>
                        <Badge>Active</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Default Voice Speed</span>
                        <span className="text-sm text-muted-foreground">1.0x</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Security & Access</CardTitle>
                  <CardDescription>
                    Manage user permissions and security settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Authentication</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">SSO Integration</span>
                        <Badge variant="outline">Available</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Two-Factor Auth</span>
                        <Badge>Enabled</Badge>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">User Roles</h4>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between text-sm">
                        <span>Admin</span>
                        <span className="text-muted-foreground">3 users</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Moderator</span>
                        <span className="text-muted-foreground">12 users</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>User</span>
                        <span className="text-muted-foreground">12,832 users</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
