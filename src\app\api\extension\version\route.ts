/**
 * Extension Version Check API
 * Provides version information and update notifications for browser extension
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting for version checks
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // limit it to 500 requests per minute
});

const VersionRequestSchema = z.object({
  extensionId: z.string().optional(),
  currentVersion: z.string().optional(),
  userAgent: z.string().optional(),
});

interface ExtensionVersionInfo {
  latestVersion: string;
  releaseNotes: string;
  isSecurityUpdate: boolean;
  downloadUrl?: string;
  minimumVersion: string;
  deprecatedVersions: string[];
  updateRequired: boolean;
  releaseDate: string;
  changelog: {
    version: string;
    date: string;
    changes: string[];
    type: 'major' | 'minor' | 'patch' | 'security';
  }[];
}

export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip ?? '127.0.0.1';
    const { success } = await limiter.check(10, ip); // 10 requests per minute per IP
    
    if (!success) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429 }
      );
    }

    // Extract headers
    const extensionId = request.headers.get('X-Extension-ID');
    const currentVersion = request.headers.get('X-Extension-Version');
    const userAgent = request.headers.get('User-Agent');

    // Validate input
    const validatedData = VersionRequestSchema.parse({
      extensionId,
      currentVersion,
      userAgent,
    });

    // Get latest version info (this would typically come from your database or config)
    const versionInfo = await getLatestVersionInfo();

    // Check if update is required
    const updateRequired = currentVersion ? 
      isUpdateRequired(currentVersion, versionInfo.minimumVersion) : false;

    // Log version check for analytics
    if (extensionId && currentVersion) {
      await logVersionCheck({
        extensionId,
        currentVersion,
        latestVersion: versionInfo.latestVersion,
        userAgent: userAgent || 'unknown',
        updateRequired,
      });
    }

    // Prepare response
    const response: ExtensionVersionInfo = {
      ...versionInfo,
      updateRequired,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Extension version check error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getLatestVersionInfo(): Promise<ExtensionVersionInfo> {
  // In a real application, this would come from your database or configuration
  // For now, we'll return static information
  return {
    latestVersion: '1.2.0',
    releaseNotes: 'Performance improvements, new onboarding flow, and enhanced error reporting.',
    isSecurityUpdate: false,
    downloadUrl: process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000/extension/download' 
      : 'https://chrome.google.com/webstore/detail/tutorai/your-extension-id',
    minimumVersion: '1.0.0',
    deprecatedVersions: ['0.9.0', '0.8.0'],
    updateRequired: false,
    releaseDate: '2025-01-15T00:00:00Z',
    changelog: [
      {
        version: '1.2.0',
        date: '2025-01-15',
        changes: [
          'Added automatic update mechanism',
          'Improved settings synchronization',
          'Enhanced performance monitoring',
          'Better error reporting and analytics',
          'New onboarding flow for first-time users'
        ],
        type: 'minor'
      },
      {
        version: '1.1.0',
        date: '2025-01-01',
        changes: [
          'Enhanced AG-UI integration',
          'Improved voice synthesis',
          'Better cross-browser compatibility',
          'Performance optimizations'
        ],
        type: 'minor'
      },
      {
        version: '1.0.1',
        date: '2024-12-20',
        changes: [
          'Fixed content script injection issues',
          'Improved error handling',
          'Better popup responsiveness'
        ],
        type: 'patch'
      },
      {
        version: '1.0.0',
        date: '2024-12-15',
        changes: [
          'Initial release',
          'AG-UI integration',
          'Real-time AI tutoring',
          'Element highlighting',
          'Settings synchronization'
        ],
        type: 'major'
      }
    ]
  };
}

function isUpdateRequired(currentVersion: string, minimumVersion: string): boolean {
  const current = parseVersion(currentVersion);
  const minimum = parseVersion(minimumVersion);
  
  return compareVersions(current, minimum) < 0;
}

function parseVersion(version: string): number[] {
  return version.split('.').map(Number);
}

function compareVersions(a: number[], b: number[]): number {
  for (let i = 0; i < Math.max(a.length, b.length); i++) {
    const aPart = a[i] || 0;
    const bPart = b[i] || 0;
    
    if (aPart > bPart) return 1;
    if (aPart < bPart) return -1;
  }
  
  return 0;
}

async function logVersionCheck(data: {
  extensionId: string;
  currentVersion: string;
  latestVersion: string;
  userAgent: string;
  updateRequired: boolean;
}) {
  try {
    // Log to analytics table
    await prisma.analytics.create({
      data: {
        userId: 'system', // System-generated event
        action: 'extension_version_check',
        metadata: {
          extensionId: data.extensionId,
          currentVersion: data.currentVersion,
          latestVersion: data.latestVersion,
          userAgent: data.userAgent,
          updateRequired: data.updateRequired,
          timestamp: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('Failed to log version check:', error);
    // Don't throw error, as this is not critical
  }
}

export async function POST(request: NextRequest) {
  // Handle extension update notifications
  try {
    const body = await request.json();
    const { extensionId, action, version, previousVersion } = body;

    if (action === 'update_completed') {
      await prisma.analytics.create({
        data: {
          userId: 'system',
          action: 'extension_update_completed',
          metadata: {
            extensionId,
            version,
            previousVersion,
            timestamp: new Date().toISOString(),
          },
        },
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Extension update notification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
