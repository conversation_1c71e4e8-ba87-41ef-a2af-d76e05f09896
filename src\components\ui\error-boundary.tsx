"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Home, 
  ChevronDown,
  ChevronUp,
  Copy,
  CheckCircle
} from "lucide-react";

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  copied: boolean;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showReload?: boolean;
  showHome?: boolean;
  className?: string;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      copied: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Report to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      // You can integrate with services like Sentry here
      // Sentry.captureException(error, { contexts: { react: errorInfo } });
    }
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      copied: false,
    });
  };

  toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  copyErrorDetails = async () => {
    const { error, errorInfo } = this.state;
    const errorText = `
Error: ${error?.message || 'Unknown error'}
Stack: ${error?.stack || 'No stack trace'}
Component Stack: ${errorInfo?.componentStack || 'No component stack'}
    `.trim();

    try {
      await navigator.clipboard.writeText(errorText);
      this.setState({ copied: true });
      setTimeout(() => this.setState({ copied: false }), 2000);
    } catch (err) {
      console.error('Failed to copy error details:', err);
    }
  };

  render() {
    const { hasError, error, errorInfo, showDetails, copied } = this.state;
    const { children, fallback, showReload = true, showHome = true, className } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return (
        <div className={cn("min-h-[400px] flex items-center justify-center p-4", className)}>
          <Card className="w-full max-w-2xl">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle className="text-xl">Something went wrong</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert variant="destructive">
                <Bug className="h-4 w-4" />
                <AlertTitle>Error Details</AlertTitle>
                <AlertDescription className="mt-2">
                  {error?.message || 'An unexpected error occurred'}
                </AlertDescription>
              </Alert>

              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <Button onClick={this.handleRetry} className="gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
                
                {showReload && (
                  <Button variant="outline" onClick={this.handleReload} className="gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Reload Page
                  </Button>
                )}
                
                {showHome && (
                  <Button variant="outline" onClick={this.handleGoHome} className="gap-2">
                    <Home className="h-4 w-4" />
                    Go Home
                  </Button>
                )}
              </div>

              {process.env.NODE_ENV === 'development' && (
                <div className="border-t pt-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={this.toggleDetails}
                    className="gap-2 w-full justify-center"
                  >
                    {showDetails ? (
                      <>
                        <ChevronUp className="h-4 w-4" />
                        Hide Details
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-4 w-4" />
                        Show Details
                      </>
                    )}
                  </Button>

                  {showDetails && (
                    <div className="mt-4 space-y-3">
                      <div className="flex justify-between items-center">
                        <h4 className="text-sm font-medium">Error Information</h4>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={this.copyErrorDetails}
                          className="gap-2"
                        >
                          {copied ? (
                            <>
                              <CheckCircle className="h-3 w-3" />
                              Copied
                            </>
                          ) : (
                            <>
                              <Copy className="h-3 w-3" />
                              Copy
                            </>
                          )}
                        </Button>
                      </div>
                      
                      <div className="bg-muted p-3 rounded-md text-xs font-mono overflow-auto max-h-40">
                        <div className="space-y-2">
                          <div>
                            <strong>Error:</strong> {error?.message}
                          </div>
                          {error?.stack && (
                            <div>
                              <strong>Stack:</strong>
                              <pre className="whitespace-pre-wrap mt-1">{error.stack}</pre>
                            </div>
                          )}
                          {errorInfo?.componentStack && (
                            <div>
                              <strong>Component Stack:</strong>
                              <pre className="whitespace-pre-wrap mt-1">{errorInfo.componentStack}</pre>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return children;
  }
}

// Hook-based error boundary for functional components
interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  className?: string;
}

export function ErrorFallback({ error, resetError, className }: ErrorFallbackProps) {
  return (
    <div className={cn("flex items-center justify-center p-4", className)}>
      <Alert variant="destructive" className="max-w-md">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription className="mt-2 space-y-3">
          <p>{error.message}</p>
          <Button onClick={resetError} size="sm" variant="outline">
            Try again
          </Button>
        </AlertDescription>
      </Alert>
    </div>
  );
}

// Simple error boundary wrapper
interface SimpleErrorBoundaryProps {
  children: ReactNode;
  message?: string;
}

export function SimpleErrorBoundary({ 
  children, 
  message = "Something went wrong" 
}: SimpleErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <Alert variant="destructive" className="m-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      }
    >
      {children}
    </ErrorBoundary>
  );
}

// Network error component
interface NetworkErrorProps {
  onRetry?: () => void;
  message?: string;
  className?: string;
}

export function NetworkError({ 
  onRetry, 
  message = "Network error occurred",
  className 
}: NetworkErrorProps) {
  return (
    <div className={cn("flex items-center justify-center p-4", className)}>
      <Alert variant="destructive" className="max-w-md">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Connection Error</AlertTitle>
        <AlertDescription className="mt-2 space-y-3">
          <p>{message}</p>
          {onRetry && (
            <Button onClick={onRetry} size="sm" variant="outline" className="gap-2">
              <RefreshCw className="h-4 w-4" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    </div>
  );
}
