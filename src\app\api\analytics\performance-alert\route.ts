/**
 * Performance Alert Analytics API
 * Tracks extension performance alerts and optimization patterns
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting for analytics
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 1000, // limit it to 1000 requests per minute
});

const PerformanceAlertSchema = z.object({
  extensionId: z.string(),
  alert: z.object({
    type: z.string(),
    severity: z.enum(['info', 'warning', 'critical']),
    data: z.record(z.any()),
    timestamp: z.string(),
  }),
  timestamp: z.string(),
  userAgent: z.string(),
  settings: z.object({
    preloadContent: z.boolean().optional(),
    tutorialDelay: z.number().optional(),
    maxCacheSize: z.number().optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip ?? '127.0.0.1';
    const { success } = await limiter.check(30, ip); // 30 requests per minute per IP
    
    if (!success) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const validatedData = PerformanceAlertSchema.parse(body);

    // Store performance alert analytics
    await prisma.analytics.create({
      data: {
        userId: 'system', // System-generated event
        action: 'extension_performance_alert',
        metadata: {
          extensionId: validatedData.extensionId,
          alertType: validatedData.alert.type,
          alertSeverity: validatedData.alert.severity,
          alertData: validatedData.alert.data,
          alertTimestamp: validatedData.alert.timestamp,
          userAgent: validatedData.userAgent,
          settings: validatedData.settings,
          timestamp: validatedData.timestamp,
        },
      },
    });

    // Check if this is a critical alert that needs immediate attention
    if (validatedData.alert.severity === 'critical') {
      await handleCriticalAlert(validatedData);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Performance alert analytics error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleCriticalAlert(alertData: any) {
  try {
    // Log critical alerts for monitoring
    console.warn('Critical performance alert received:', {
      extensionId: alertData.extensionId,
      type: alertData.alert.type,
      data: alertData.alert.data,
    });

    // You could add additional handling here:
    // - Send notifications to administrators
    // - Trigger automatic scaling
    // - Update performance thresholds
    // - Generate incident reports

    // For now, just create an audit log entry
    await prisma.auditLog.create({
      data: {
        action: 'critical_performance_alert',
        resource: 'extension',
        resourceId: alertData.extensionId,
        metadata: {
          alertType: alertData.alert.type,
          alertData: alertData.alert.data,
          userAgent: alertData.userAgent,
          settings: alertData.settings,
        },
        ipAddress: '127.0.0.1', // Extension alerts don't have real IP
        userAgent: alertData.userAgent,
      },
    });

  } catch (error) {
    console.error('Failed to handle critical alert:', error);
    // Don't throw error, as this is not critical for the main flow
  }
}
