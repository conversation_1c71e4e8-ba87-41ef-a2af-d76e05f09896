import React from "react";
import { motion } from "framer-motion";
import { ProgressIndicatorProps } from "./types";
import { cn } from "@/lib/utils";

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  current,
  total,
  theme = "auto",
}) => {
  const progress = total > 0 ? (current / total) * 100 : 0;
  const isDark =
    theme === "dark" ||
    (theme === "auto" &&
      window.matchMedia("(prefers-color-scheme: dark)").matches);

  return (
    <div className="flex items-center space-x-3">
      {/* Step indicators */}
      <div className="flex items-center space-x-1">
        {Array.from({ length: total }, (_, index) => {
          const isActive = index === current;
          const isCompleted = index < current;

          return (
            <motion.div
              key={index}
              className={cn(
                "w-2 h-2 rounded-full transition-colors duration-200",
                isActive
                  ? "bg-primary"
                  : isCompleted
                    ? "bg-primary/60"
                    : isDark
                      ? "bg-white/20"
                      : "bg-black/20",
              )}
              initial={{ scale: 0.8, opacity: 0.5 }}
              animate={{
                scale: isActive ? 1.2 : 1,
                opacity: isActive || isCompleted ? 1 : 0.5,
              }}
              transition={{ duration: 0.2 }}
            />
          );
        })}
      </div>

      {/* Progress bar */}
      <div
        className={cn(
          "flex-1 h-1 rounded-full overflow-hidden",
          isDark ? "bg-white/10" : "bg-black/10",
        )}
      >
        <motion.div
          className="h-full bg-primary rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        />
      </div>

      {/* Step counter */}
      <div
        className={cn(
          "text-xs font-medium tabular-nums",
          isDark ? "text-white/70" : "text-black/70",
        )}
      >
        {current + 1} / {total}
      </div>
    </div>
  );
};

export default ProgressIndicator;
