import React, { useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TooltipComponentProps } from "./types";
import { cn } from "@/lib/utils";
import NavigationControls from "./NavigationControls";
import ProgressIndicator from "./ProgressIndicator";
import { Info, Lightbulb, Target } from "lucide-react";

const TooltipComponent: React.FC<TooltipComponentProps> = ({
  step,
  position,
  isVisible,
  theme = "auto",
  onNext,
  onPrevious,
  onSkip,
  onClose,
  navigationState,
  animationDuration = 300,
}) => {
  const tooltipRef = useRef<HTMLDivElement>(null);
  const isDark =
    theme === "dark" ||
    (theme === "auto" &&
      window.matchMedia("(prefers-color-scheme: dark)").matches);

  // Update parent with tooltip dimensions
  useEffect(() => {
    if (tooltipRef.current && isVisible) {
      const rect = tooltipRef.current.getBoundingClientRect();
      // Trigger recalculation if needed
    }
  }, [isVisible, step]);

  if (!isVisible || !position) {
    return null;
  }

  const getStepIcon = () => {
    switch (step.action?.type) {
      case "click":
        return <Target className="h-4 w-4" />;
      case "hover":
        return <Info className="h-4 w-4" />;
      default:
        return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getArrowClasses = () => {
    const baseClasses = "absolute w-0 h-0 border-solid";
    const arrowSize = 8;
    const borderColor = isDark ? "border-gray-800" : "border-white";
    const shadowColor = isDark ? "drop-shadow-lg" : "drop-shadow-md";

    switch (position.arrow.side) {
      case "top":
        return cn(
          baseClasses,
          `border-l-[${arrowSize}px] border-r-[${arrowSize}px] border-b-[${arrowSize}px]`,
          `border-l-transparent border-r-transparent ${borderColor}`,
          shadowColor,
        );
      case "bottom":
        return cn(
          baseClasses,
          `border-l-[${arrowSize}px] border-r-[${arrowSize}px] border-t-[${arrowSize}px]`,
          `border-l-transparent border-r-transparent ${borderColor}`,
          shadowColor,
        );
      case "left":
        return cn(
          baseClasses,
          `border-t-[${arrowSize}px] border-b-[${arrowSize}px] border-r-[${arrowSize}px]`,
          `border-t-transparent border-b-transparent ${borderColor}`,
          shadowColor,
        );
      case "right":
        return cn(
          baseClasses,
          `border-t-[${arrowSize}px] border-b-[${arrowSize}px] border-l-[${arrowSize}px]`,
          `border-t-transparent border-b-transparent ${borderColor}`,
          shadowColor,
        );
      default:
        return "";
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          ref={tooltipRef}
          className="fixed z-[10002] pointer-events-auto"
          style={{
            left: position.x,
            top: position.y,
          }}
          initial={{
            opacity: 0,
            scale: 0.9,
            y:
              position.placement === "top"
                ? 10
                : position.placement === "bottom"
                  ? -10
                  : 0,
            x:
              position.placement === "left"
                ? 10
                : position.placement === "right"
                  ? -10
                  : 0,
          }}
          animate={{
            opacity: 1,
            scale: 1,
            y: 0,
            x: 0,
          }}
          exit={{
            opacity: 0,
            scale: 0.9,
            y:
              position.placement === "top"
                ? 10
                : position.placement === "bottom"
                  ? -10
                  : 0,
            x:
              position.placement === "left"
                ? 10
                : position.placement === "right"
                  ? -10
                  : 0,
          }}
          transition={{
            duration: animationDuration / 1000,
            ease: "easeOut",
          }}
        >
          {/* Arrow */}
          <div
            className={getArrowClasses()}
            style={{
              left: position.arrow.x,
              top: position.arrow.y,
              transform: "translate(-50%, -50%)",
            }}
          />

          {/* Tooltip content */}
          <Card
            className={cn(
              "w-80 max-w-sm shadow-xl border-2",
              isDark
                ? "bg-gray-900 border-gray-700 text-white"
                : "bg-white border-gray-200 text-gray-900",
            )}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between space-x-3">
                <div className="flex items-center space-x-2">
                  <div
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full",
                      isDark
                        ? "bg-blue-500/20 text-blue-400"
                        : "bg-blue-500/10 text-blue-600",
                    )}
                  >
                    {getStepIcon()}
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-sm font-semibold leading-tight">
                      {step.title}
                    </CardTitle>
                  </div>
                </div>
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs px-2 py-0.5 shrink-0",
                    isDark
                      ? "border-gray-600 text-gray-300"
                      : "border-gray-300 text-gray-600",
                  )}
                >
                  Step {navigationState.currentStep + 1}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Description */}
              <div
                className={cn(
                  "text-sm leading-relaxed",
                  isDark ? "text-gray-300" : "text-gray-600",
                )}
              >
                {step.description}
              </div>

              {/* Action hint */}
              {step.action && (
                <div
                  className={cn(
                    "flex items-center space-x-2 p-2 rounded-md text-xs",
                    isDark
                      ? "bg-yellow-500/10 text-yellow-400 border border-yellow-500/20"
                      : "bg-yellow-50 text-yellow-700 border border-yellow-200",
                  )}
                >
                  <Info className="h-3 w-3 shrink-0" />
                  <span>
                    {step.action.type === "click" &&
                      "Click on the highlighted element"}
                    {step.action.type === "hover" &&
                      "Hover over the highlighted element"}
                    {step.action.type === "focus" &&
                      "Focus on the highlighted element"}
                    {step.action.type === "scroll" &&
                      "Scroll to see the highlighted element"}
                    {step.action.type === "wait" &&
                      `Wait ${step.action.duration || 3} seconds`}
                  </span>
                </div>
              )}

              {/* Progress indicator */}
              <div className="pt-2">
                <ProgressIndicator
                  current={navigationState.currentStep}
                  total={navigationState.totalSteps}
                  theme={theme}
                />
              </div>

              {/* Navigation controls */}
              <div className="pt-2 border-t border-border">
                <NavigationControls
                  navigationState={navigationState}
                  onNext={onNext}
                  onPrevious={onPrevious}
                  onSkip={onSkip}
                  onClose={onClose}
                  allowSkip={true}
                  theme={theme}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TooltipComponent;
