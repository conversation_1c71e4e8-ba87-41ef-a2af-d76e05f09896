"use client";

import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useOnboarding } from './OnboardingProvider';
import { cn } from '@/lib/utils';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Skip, 
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { useFocusTrap, useKeyboardNavigation } from '@/hooks/useAccessibility';
import { KeyboardKeys } from '@/lib/accessibility';

interface HighlightedElement {
  element: HTMLElement;
  rect: DOMRect;
}

export function OnboardingOverlay() {
  const {
    isActive,
    currentFlow,
    currentStep,
    currentStepIndex,
    nextStep,
    previousStep,
    skipStep,
    stopFlow,
    pauseFlow,
    resumeFlow,
    settings,
  } = useOnboarding();

  const [highlightedElement, setHighlightedElement] = useState<HighlightedElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);
  const focusTrapRef = useFocusTrap(isActive);

  // Handle keyboard navigation
  const handleKeyDown = useKeyboardNavigation({
    ESCAPE: stopFlow,
    ARROW_LEFT: previousStep,
    ARROW_RIGHT: nextStep,
    ENTER: nextStep,
    SPACE: nextStep,
  }, [stopFlow, previousStep, nextStep]);

  useEffect(() => {
    if (isActive) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isActive, handleKeyDown]);

  // Update highlighted element when step changes
  useEffect(() => {
    if (!isActive || !currentStep?.target) {
      setHighlightedElement(null);
      return;
    }

    const element = document.querySelector(currentStep.target) as HTMLElement;
    if (element) {
      const rect = element.getBoundingClientRect();
      setHighlightedElement({ element, rect });
      
      // Scroll element into view
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center',
        inline: 'center'
      });

      // Calculate tooltip position
      const tooltipX = rect.left + rect.width / 2;
      const tooltipY = currentStep.position === 'top' 
        ? rect.top - 20 
        : rect.bottom + 20;
      
      setTooltipPosition({ x: tooltipX, y: tooltipY });
    }
  }, [isActive, currentStep]);

  // Prevent body scroll when overlay is active
  useEffect(() => {
    if (isActive) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [isActive]);

  if (!isActive || !currentFlow || !currentStep) {
    return null;
  }

  const progress = ((currentStepIndex + 1) / currentFlow.steps.length) * 100;

  const overlayContent = (
    <motion.div
      ref={focusTrapRef}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center"
      role="dialog"
      aria-modal="true"
      aria-labelledby="onboarding-title"
      aria-describedby="onboarding-description"
    >
      {/* Backdrop with cutout for highlighted element */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm">
        {highlightedElement && (
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="absolute border-2 border-primary rounded-lg shadow-lg"
            style={{
              left: highlightedElement.rect.left - 4,
              top: highlightedElement.rect.top - 4,
              width: highlightedElement.rect.width + 8,
              height: highlightedElement.rect.height + 8,
              background: 'transparent',
              boxShadow: `
                0 0 0 4px rgba(59, 130, 246, 0.3),
                0 0 0 9999px rgba(0, 0, 0, 0.5)
              `,
            }}
          />
        )}
      </div>

      {/* Tooltip */}
      <motion.div
        initial={{ scale: 0.8, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.8, opacity: 0, y: 20 }}
        className={cn(
          "absolute max-w-sm z-10",
          currentStep.position === 'center' && "relative"
        )}
        style={
          currentStep.position !== 'center' 
            ? {
                left: tooltipPosition.x - 200, // Center the 400px wide tooltip
                top: tooltipPosition.y,
                transform: 'translateX(-50%)',
              }
            : {}
        }
      >
        <Card className="shadow-xl border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle id="onboarding-title" className="text-lg">
                  {currentStep.title}
                </CardTitle>
                {currentStep.optional && (
                  <Badge variant="secondary" className="text-xs">
                    Optional
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={stopFlow}
                className="h-6 w-6"
                aria-label="Close onboarding"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {settings.showProgress && (
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Step {currentStepIndex + 1} of {currentFlow.steps.length}</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-1" />
              </div>
            )}
          </CardHeader>

          <CardContent className="space-y-4">
            <p id="onboarding-description" className="text-sm text-muted-foreground">
              {currentStep.description}
            </p>

            {currentStep.component && (
              <div className="border-t pt-4">
                {currentStep.component}
              </div>
            )}

            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={previousStep}
                  disabled={currentStepIndex === 0}
                  className="gap-1"
                >
                  <ChevronLeft className="h-3 w-3" />
                  Back
                </Button>
                
                {settings.allowSkip && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={skipStep}
                    className="gap-1"
                  >
                    <Skip className="h-3 w-3" />
                    Skip
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={stopFlow}
                  className="gap-1"
                >
                  <X className="h-3 w-3" />
                  Exit
                </Button>
                
                <Button
                  onClick={nextStep}
                  size="sm"
                  className="gap-1"
                >
                  {currentStepIndex === currentFlow.steps.length - 1 ? (
                    'Finish'
                  ) : (
                    <>
                      Next
                      <ChevronRight className="h-3 w-3" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Flow info */}
      <div className="absolute top-4 left-4">
        <Card className="bg-background/80 backdrop-blur-sm">
          <CardContent className="p-3">
            <div className="flex items-center gap-2 text-sm">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
              <span className="font-medium">{currentFlow.name}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {currentFlow.description}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={pauseFlow}
          className="gap-1"
        >
          <Pause className="h-3 w-3" />
          Pause
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            stopFlow();
            setTimeout(() => {
              if (currentFlow) {
                // Restart flow logic would go here
              }
            }, 100);
          }}
          className="gap-1"
        >
          <RotateCcw className="h-3 w-3" />
          Restart
        </Button>
      </div>
    </motion.div>
  );

  return createPortal(
    <AnimatePresence>
      {overlayContent}
    </AnimatePresence>,
    document.body
  );
}
