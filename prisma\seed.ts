import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";
import { defaultUserSettings } from "../src/models/User";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seed...");

  try {
    // Create admin user
    const adminEmail = "<EMAIL>";
    const adminExists = await prisma.user.findUnique({
      where: { email: adminEmail },
    });

    if (!adminExists) {
      const hashedPassword = await bcrypt.hash("SecureAdmin123!", 12);

      const admin = await prisma.user.create({
        data: {
          name: "System Administrator",
          email: adminEmail,
          password: hashedPassword,
          role: "ADMIN",
          subscription: "premium",
          settings: defaultUserSettings as any,
          isActive: true,
          emailVerified: new Date(),
        },
      });

      console.log(`✅ Created admin user: ${admin.email}`);
    } else {
      console.log("ℹ️ Admin user already exists");
    }

    // Create demo user
    const demoEmail = "<EMAIL>";
    const demoExists = await prisma.user.findUnique({
      where: { email: demoEmail },
    });

    if (!demoExists) {
      const hashedPassword = await bcrypt.hash("DemoUser123!", 12);

      const demo = await prisma.user.create({
        data: {
          name: "Demo User",
          email: demoEmail,
          password: hashedPassword,
          role: "USER",
          subscription: "free",
          settings: defaultUserSettings as any,
          isActive: true,
          emailVerified: new Date(),
        },
      });

      console.log(`✅ Created demo user: ${demo.email}`);
    } else {
      console.log("ℹ️ Demo user already exists");
    }

    // Create sample tutorial
    const tutorialExists = await prisma.tutorial.findFirst({
      where: { title: "Getting Started with TutorAI" },
    });

    if (!tutorialExists) {
      const admin = await prisma.user.findUnique({
        where: { email: adminEmail },
      });

      if (admin) {
        const tutorial = await prisma.tutorial.create({
          data: {
            title: "Getting Started with TutorAI",
            description:
              "Learn how to use TutorAI to enhance your learning experience",
            steps: {
              steps: [
                {
                  id: 1,
                  title: "Install the Browser Extension",
                  description:
                    "Download and install the TutorAI browser extension",
                  selector: null,
                  action: "highlight",
                  content:
                    "Click on the extension icon in your browser toolbar to get started.",
                },
                {
                  id: 2,
                  title: "Activate TutorAI",
                  description: "Click the TutorAI icon to activate the overlay",
                  selector: ".tutor-ai-icon",
                  action: "click",
                  content:
                    "The TutorAI overlay will appear, ready to help you learn.",
                },
                {
                  id: 3,
                  title: "Get Explanations",
                  description:
                    "Hover over any element to get AI-powered explanations",
                  selector: null,
                  action: "hover",
                  content:
                    "Move your cursor over any webpage element to see detailed explanations.",
                },
              ],
            },
            language: "en",
            isActive: true,
            createdBy: admin.id,
            metadata: {
              difficulty: "beginner",
              estimatedTime: 5,
              category: "getting-started",
            },
          },
        });

        console.log(`✅ Created sample tutorial: ${tutorial.title}`);
      }
    } else {
      console.log("ℹ️ Sample tutorial already exists");
    }

    // Create role permissions
    const rolePermissions = [
      // USER permissions
      { role: "USER", permission: "TUTORIAL_READ" },
      { role: "USER", permission: "AI_USAGE_READ" },

      // MODERATOR permissions
      { role: "MODERATOR", permission: "TUTORIAL_READ" },
      { role: "MODERATOR", permission: "TUTORIAL_WRITE" },
      { role: "MODERATOR", permission: "TUTORIAL_PUBLISH" },
      { role: "MODERATOR", permission: "AI_USAGE_READ" },
      { role: "MODERATOR", permission: "ANALYTICS_READ" },
      { role: "MODERATOR", permission: "USER_READ" },

      // ADMIN permissions
      { role: "ADMIN", permission: "USER_READ" },
      { role: "ADMIN", permission: "USER_WRITE" },
      { role: "ADMIN", permission: "USER_DELETE" },
      { role: "ADMIN", permission: "TUTORIAL_READ" },
      { role: "ADMIN", permission: "TUTORIAL_WRITE" },
      { role: "ADMIN", permission: "TUTORIAL_DELETE" },
      { role: "ADMIN", permission: "TUTORIAL_PUBLISH" },
      { role: "ADMIN", permission: "ANALYTICS_READ" },
      { role: "ADMIN", permission: "ANALYTICS_EXPORT" },
      { role: "ADMIN", permission: "AI_USAGE_READ" },
      { role: "ADMIN", permission: "AI_USAGE_MANAGE" },
      { role: "ADMIN", permission: "BILLING_READ" },
      { role: "ADMIN", permission: "BILLING_WRITE" },
      { role: "ADMIN", permission: "AUDIT_LOG_READ" },

      // SUPER_ADMIN permissions (all permissions)
      { role: "SUPER_ADMIN", permission: "USER_READ" },
      { role: "SUPER_ADMIN", permission: "USER_WRITE" },
      { role: "SUPER_ADMIN", permission: "USER_DELETE" },
      { role: "SUPER_ADMIN", permission: "TUTORIAL_READ" },
      { role: "SUPER_ADMIN", permission: "TUTORIAL_WRITE" },
      { role: "SUPER_ADMIN", permission: "TUTORIAL_DELETE" },
      { role: "SUPER_ADMIN", permission: "TUTORIAL_PUBLISH" },
      { role: "SUPER_ADMIN", permission: "ANALYTICS_READ" },
      { role: "SUPER_ADMIN", permission: "ANALYTICS_EXPORT" },
      { role: "SUPER_ADMIN", permission: "AI_USAGE_READ" },
      { role: "SUPER_ADMIN", permission: "AI_USAGE_MANAGE" },
      { role: "SUPER_ADMIN", permission: "BILLING_READ" },
      { role: "SUPER_ADMIN", permission: "BILLING_WRITE" },
      { role: "SUPER_ADMIN", permission: "AUDIT_LOG_READ" },
      { role: "SUPER_ADMIN", permission: "SYSTEM_CONFIG" },
    ];

    for (const rp of rolePermissions) {
      const exists = await prisma.rolePermission.findUnique({
        where: {
          role_permission: {
            role: rp.role as any,
            permission: rp.permission as any,
          },
        },
      });

      if (!exists) {
        await prisma.rolePermission.create({
          data: {
            role: rp.role as any,
            permission: rp.permission as any,
          },
        });
      }
    }

    console.log("✅ Role permissions configured");

    // Create initial analytics data
    const admin = await prisma.user.findUnique({
      where: { email: adminEmail },
    });

    if (admin) {
      const analyticsExists = await prisma.analytics.findFirst({
        where: { userId: admin.id },
      });

      if (!analyticsExists) {
        await prisma.analytics.create({
          data: {
            userId: admin.id,
            action: "system_initialized",
            metadata: {
              timestamp: new Date().toISOString(),
              version: "1.0.0",
            },
          },
        });

        console.log("✅ Initial analytics data created");
      }
    }

    console.log("🎉 Database seed completed successfully!");
    console.log("");
    console.log("📋 Login Credentials:");
    console.log("👤 Admin: <EMAIL> / SecureAdmin123!");
    console.log("👤 Demo:  <EMAIL> / DemoUser123!");
    console.log("");
    console.log("⚠️  Please change these passwords in production!");
  } catch (error) {
    console.error("❌ Error during seed:", error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
