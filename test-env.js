// Quick test to verify environment validation
const fs = require('fs');

// Read .env file
const envContent = fs.readFileSync('.env', 'utf8');
console.log('Current .env content:');
console.log(envContent);

// Check NEXTAUTH_SECRET length
const lines = envContent.split('\n');
const nextAuthLine = lines.find(line => line.startsWith('NEXTAUTH_SECRET='));

if (nextAuthLine) {
  const secret = nextAuthLine.split('=')[1].replace(/"/g, '');
  console.log(`\nNEXTAUTH_SECRET length: ${secret.length} characters`);
  console.log(`Valid (>=32 chars): ${secret.length >= 32 ? '✅ YES' : '❌ NO'}`);
} else {
  console.log('❌ NEXTAUTH_SECRET not found');
}
