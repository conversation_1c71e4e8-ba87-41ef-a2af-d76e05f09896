/* TutorAI Extension Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #1f2937;
  background: #ffffff;
}

.popup-container {
  width: 380px;
  min-height: 500px;
  position: relative;
}

/* Header */
.header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 24px;
  height: 24px;
}

.header h1 {
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
  transition: background-color 0.3s ease;
}

.status-dot.connected {
  background: #10b981;
}

/* Content */
.content {
  padding: 20px;
}

.section {
  margin-bottom: 24px;
}

.section h3 {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
  transform: translateY(-1px);
}

.btn-secondary.active {
  background: #dbeafe;
  color: #1d4ed8;
  border-color: #3b82f6;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn .icon {
  font-size: 16px;
}

/* Page Info */
.page-info {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
}

.page-title {
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-url {
  font-size: 12px;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* AI Response */
.ai-response {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 12px;
}

.response-text {
  color: #0c4a6e;
  margin-bottom: 8px;
  line-height: 1.5;
  max-height: 120px;
  overflow-y: auto;
}

.response-actions {
  display: flex;
  justify-content: flex-end;
}

/* Settings */
.settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #374151;
}

.setting-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #3b82f6;
}

.setting-item input[type="color"] {
  width: 32px;
  height: 24px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
}

/* Footer */
.footer {
  border-top: 1px solid #e5e7eb;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
}

.links {
  display: flex;
  gap: 16px;
}

.links a {
  color: #6b7280;
  text-decoration: none;
  font-size: 12px;
  transition: color 0.2s ease;
}

.links a:hover {
  color: #3b82f6;
}

.version {
  font-size: 11px;
  color: #9ca3af;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  z-index: 1000;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6b7280;
  font-size: 14px;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Update notifications */
.update-notification {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.update-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.update-icon {
  font-size: 16px;
}

.update-title {
  font-weight: 600;
  flex: 1;
}

.security-badge {
  background: #ef4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
}

.update-content p {
  margin-bottom: 12px;
  font-size: 13px;
  opacity: 0.9;
}

.update-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-text {
  background: transparent;
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-text:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #374151;
}

.modal-body {
  padding: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.changelog-content {
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Error notifications */
.error-notification {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  color: #ef4444;
  font-size: 16px;
}

.error-message {
  flex: 1;
  color: #dc2626;
  font-size: 13px;
}

.error-close {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-close:hover {
  color: #b91c1c;
}

/* Settings actions */
.setting-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* Advanced settings modal */
.advanced-settings-modal .modal-content {
  max-width: 500px;
}

.advanced-settings {
  max-height: 400px;
  overflow-y: auto;
}

.setting-group {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.setting-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-group h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 13px;
}

.setting-item input[type="checkbox"] {
  margin-right: 8px;
}

.setting-item input[type="number"],
.setting-item select {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  min-width: 80px;
}

/* Backup manager modal */
.backup-manager-modal .modal-content {
  max-width: 500px;
}

.backup-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.backup-list h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.backup-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
}

.backup-info {
  flex: 1;
}

.backup-id {
  font-weight: 500;
  font-size: 13px;
  color: #374151;
}

.backup-date {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

.backup-version {
  font-size: 11px;
  color: #3b82f6;
  margin-top: 2px;
}

.backup-actions {
  margin-left: 12px;
}

.no-backups {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 20px;
}

/* Confirm modal */
.confirm-modal .modal-content {
  max-width: 400px;
}

.confirm-modal .modal-body p {
  margin-bottom: 12px;
  line-height: 1.5;
}

.btn-destructive {
  background: #ef4444;
  color: white;
  border: 1px solid #dc2626;
}

.btn-destructive:hover {
  background: #dc2626;
}

/* Success notifications */
.success-notification {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-icon {
  color: #16a34a;
  font-size: 16px;
}

.success-message {
  flex: 1;
  color: #15803d;
  font-size: 13px;
}

.success-close {
  background: none;
  border: none;
  color: #15803d;
  cursor: pointer;
  font-size: 16px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-close:hover {
  color: #166534;
}

/* Performance dashboard */
.performance-dashboard-modal .modal-content {
  max-width: 600px;
}

.performance-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.perf-stat {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.perf-label {
  font-size: 11px;
  color: #64748b;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 4px;
}

.perf-value {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.performance-details {
  max-height: 400px;
  overflow-y: auto;
}

.perf-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.perf-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.perf-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.perf-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 13px;
}

.metric-item span:first-child {
  color: #6b7280;
}

.metric-item span:last-child {
  font-weight: 500;
  color: #374151;
}

.error-count {
  color: #ef4444 !important;
  font-weight: 600 !important;
}

.no-alerts,
.no-recommendations {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
}

.alert-item.critical {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.alert-item.warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
  color: #d97706;
}

.alert-item.info {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  color: #2563eb;
}

.alert-type {
  font-weight: 500;
  text-transform: capitalize;
}

.alert-time {
  font-size: 11px;
  opacity: 0.8;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  font-size: 13px;
  gap: 12px;
}

.recommendation-item.warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.recommendation-item.info {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

.rec-message {
  flex: 1;
  line-height: 1.4;
}

.apply-recommendation {
  white-space: nowrap;
}

/* Error dashboard */
.error-dashboard-modal .modal-content {
  max-width: 600px;
}

.error-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.error-stat {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.error-label {
  font-size: 11px;
  color: #64748b;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 4px;
}

.error-value {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.pending-errors {
  color: #f59e0b !important;
}

.error-details {
  max-height: 400px;
  overflow-y: auto;
}

.error-section {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.error-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.error-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.no-data {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
}

/* Severity chart */
.severity-chart {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.severity-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.severity-bar {
  flex: 1;
  height: 20px;
  background: #f1f5f9;
  border-radius: 10px;
  overflow: hidden;
}

.severity-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.severity-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 80px;
  font-size: 12px;
}

.severity-name {
  text-transform: capitalize;
  color: #374151;
}

.severity-count {
  font-weight: 600;
  color: #1f2937;
}

/* Category chart */
.category-chart {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 13px;
}

.category-name {
  text-transform: capitalize;
  color: #374151;
}

.category-count {
  font-weight: 600;
  color: #1f2937;
  background: #e2e8f0;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

/* Recent errors list */
.recent-errors-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  font-size: 12px;
}

.error-item.critical {
  background: #fef2f2;
  border-color: #fecaca;
}

.error-item.high {
  background: #fff7ed;
  border-color: #fed7aa;
}

.error-item.medium {
  background: #fefce8;
  border-color: #fde68a;
}

.error-item.low {
  background: #eff6ff;
  border-color: #bfdbfe;
}

.error-item.info {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.error-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.error-type {
  font-weight: 600;
  color: #374151;
  text-transform: capitalize;
}

.error-time {
  color: #6b7280;
  font-size: 11px;
}

.error-message {
  color: #1f2937;
  margin-bottom: 8px;
  line-height: 1.4;
}

.error-meta {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 11px;
}

.error-category,
.error-severity {
  padding: 2px 6px;
  border-radius: 4px;
  background: #e5e7eb;
  color: #374151;
  text-transform: capitalize;
}

.error-reported {
  color: #059669;
  font-weight: 500;
}

.error-pending {
  color: #d97706;
  font-weight: 500;
}