@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    }
    
    /* Smooth theme transitions for all elements */
    *,
    *::before,
    *::after {
      transition:
        background-color 0.2s ease-in-out,
        border-color 0.2s ease-in-out,
        color 0.2s ease-in-out,
        fill 0.2s ease-in-out,
        stroke 0.2s ease-in-out,
        box-shadow 0.2s ease-in-out;
    }
    
    /* Respect user's motion preferences */
    @media (prefers-reduced-motion: reduce) {
    
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }
    }
    
    /* High contrast mode support */
    @media (prefers-contrast: high) {
      :root {
        --border: 0 0% 20%;
        --input: 0 0% 20%;
      }
    
      .dark {
        --border: 0 0% 80%;
        --input: 0 0% 80%;
      }
    }
    
    /* Focus styles for better accessibility */
    :focus-visible {
      outline: 2px solid hsl(var(--ring));
      outline-offset: 2px;
    }
    
    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: hsl(var(--muted));
    }
    
    ::-webkit-scrollbar-thumb {
      background: hsl(var(--muted-foreground) / 0.3);
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: hsl(var(--muted-foreground) / 0.5);
    }
    
    /* Selection styles */
    ::selection {
      background: hsl(var(--primary) / 0.2);
      color: hsl(var(--primary-foreground));
    }
    }
    
    /* Theme-specific enhancements */
    @layer components {
    
      /* Glass morphism effect for cards in light mode */
      .glass-light {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
    
      /* Glass morphism effect for cards in dark mode */
      .glass-dark {
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
    
      /* Theme-aware shadows */
      .shadow-theme {
        box-shadow:
          0 1px 3px 0 hsl(var(--foreground) / 0.1),
          0 1px 2px 0 hsl(var(--foreground) / 0.06);
      }
    
      .shadow-theme-lg {
        box-shadow:
          0 10px 15px -3px hsl(var(--foreground) / 0.1),
          0 4px 6px -2px hsl(var(--foreground) / 0.05);
      }
    
      /* Screen reader only content */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }
    
      .sr-only.focus:focus,
      .sr-only:focus-visible {
        position: static;
        width: auto;
        height: auto;
        padding: 0.5rem 1rem;
        margin: 0;
        overflow: visible;
        clip: auto;
        white-space: normal;
        background: hsl(var(--primary));
        color: hsl(var(--primary-foreground));
        border-radius: 0.375rem;
        z-index: 50;
  }
}
