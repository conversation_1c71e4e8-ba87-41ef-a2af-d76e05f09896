# =============================================================================
# TutorAI Environment Configuration
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env to version control!

# =============================================================================
# DATABASE CONFIGURATION (Required)
# =============================================================================
# Option 1: Full database URL (recommended)
DATABASE_URL="postgresql://username:password@localhost:5432/tutorai_db?schema=public"

# Option 2: Individual components (used if DATABASE_URL is not set)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your-secure-password
DB_NAME=tutorai_db
DB_SCHEMA=public

# =============================================================================
# AUTHENTICATION CONFIGURATION (Required)
# =============================================================================
# Your application URL
NEXTAUTH_URL="https://yourdomain.com"

# Generate a secure secret: openssl rand -base64 32
NEXTAUTH_SECRET="your-super-secure-nextauth-secret-min-32-chars"

# =============================================================================
# OAUTH PROVIDERS (Optional)
# =============================================================================
# Google OAuth - Get from Google Cloud Console
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# =============================================================================
# AI PROVIDERS (At least one required)
# =============================================================================
# OpenAI API Key - Get from https://platform.openai.com/api-keys
OPENAI_API_KEY="sk-your-openai-api-key"

# Anthropic API Key - Get from https://console.anthropic.com/
ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"

# Groq API Key - Get from https://console.groq.com/
GROQ_API_KEY="gsk_your-groq-api-key"

# OpenRouter API Key - Get from https://openrouter.ai/keys
OPENROUTER_API_KEY="sk-or-your-openrouter-api-key"

# Google AI API Key - Get from Google AI Studio
GOOGLE_AI_API_KEY="your-google-ai-api-key"

# =============================================================================
# VOICE SYNTHESIS (Optional)
# =============================================================================
# ElevenLabs API Key - Get from https://elevenlabs.io/
ELEVENLABS_API_KEY="your-elevenlabs-api-key"

# =============================================================================
# EMAIL CONFIGURATION (Required for password reset)
# =============================================================================
# SMTP Configuration for sending emails
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM="TutorAI <<EMAIL>>"

# =============================================================================
# AG-UI CONFIGURATION
# =============================================================================
# Generate a secure API key for AG-UI
AG_UI_API_KEY="your-secure-ag-ui-api-key"
NEXT_PUBLIC_AG_UI_TRANSPORT="sse"

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
# Environment: development, staging, production
NODE_ENV=production

# Server port
PORT=3000

# Public application URL
NEXT_PUBLIC_APP_URL="https://yourdomain.com"

# API base URL
API_URL="https://yourdomain.com/api"

# =============================================================================
# SECURITY SETTINGS (Optional)
# =============================================================================
# Rate limiting settings (uses defaults if not set)
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_AUTH_WINDOW=900000
RATE_LIMIT_API_MAX=100
RATE_LIMIT_API_WINDOW=60000

# =============================================================================
# MONITORING & LOGGING (Optional)
# =============================================================================
# Sentry DSN for error tracking
SENTRY_DSN="your-sentry-dsn"

# Log level: error, warn, info, debug
LOG_LEVEL=info

# =============================================================================
# DEVELOPMENT ONLY
# =============================================================================
# These should only be used in development
# DISABLE_AUTH=false
# MOCK_EMAIL=true
# DEBUG_MODE=true
# STRICT_ENV_VALIDATION=false
