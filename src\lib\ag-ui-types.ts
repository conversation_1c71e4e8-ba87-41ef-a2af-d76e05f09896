
export interface AgentEvent {
  type: string;
  timestamp?: string;
  rawEvent?: any;
}

export interface RunStartedEvent extends AgentEvent {
  type: 'RunStarted';
  runId: string;
  threadId: string;
}

export interface RunFinishedEvent extends AgentEvent {
  type: 'RunFinished';
  runId: string;
  threadId: string;
  result?: any;
}

export interface RunErrorEvent extends AgentEvent {
  type: 'RunError';
  message: string;
  code?: string;
}

export interface TextMessageStartEvent extends AgentEvent {
  type: 'TextMessageStart';
  messageId: string;
  role: string;
}

export interface TextMessageContentEvent extends AgentEvent {
  type: 'TextMessageContent';
  messageId: string;
  delta: string;
}

export interface TextMessageEndEvent extends AgentEvent {
  type: 'TextMessageEnd';
  messageId: string;
}

export interface ToolCallStartEvent extends AgentEvent {
  type: 'ToolCallStart';
  toolCallId: string;
  toolCallName: string;
  parentMessageId?: string;
}

export interface ToolCallArgsEvent extends AgentEvent {
  type: 'ToolCallArgs';
  toolCallId: string;
  delta: string;
}

export interface ToolCallEndEvent extends AgentEvent {
  type: 'ToolCallEnd';
  toolCallId: string;
}

export interface ToolCallResultEvent extends AgentEvent {
  type: 'ToolCallResult';
  messageId: string;
  toolCallId: string;
  content: string;
  role?: string;
}

export interface StateSnapshotEvent extends AgentEvent {
  type: 'StateSnapshot';
  snapshot: any;
}

export interface StateDeltaEvent extends AgentEvent {
  type: 'StateDelta';
  delta: any[];
}

export interface StepStartedEvent extends AgentEvent {
  type: 'StepStarted';
  stepName: string;
}

export interface StepFinishedEvent extends AgentEvent {
  type: 'StepFinished';
  stepName: string;
}

export interface CustomEvent extends AgentEvent {
  type: 'Custom';
  name: string;
  value: any;
}
