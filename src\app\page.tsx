"use client";

import React from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Download,
  Globe,
  Headphones,
  Layers,
  Settings,
  Sparkles,
  Star,
  CheckCircle,
  Users,
  Zap,
  Shield,
  Play,
} from "lucide-react";
import DashboardPreview from "@/components/dashboard/DashboardPreview";
import ExtensionDemo from "@/components/extension/ExtensionDemo";
import FeatureShowcase from "@/components/features/FeatureShowcase";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  ResponsiveNavigation,
  defaultNavigationItems
} from "@/components/ui/responsive-navigation";
import {
  ResponsiveContainer,
  ResponsiveSection,
  ResponsiveGrid,
  ResponsiveStack
} from "@/components/ui/responsive-container";
import {
  OnboardingTrigger,
  useOnboardingTrigger
} from "@/components/onboarding/OnboardingManager";
import { HelpButton } from "@/components/ui/help-button";
import { textClasses } from "@/lib/utils";

export default function Home() {
  const navigationActions = (
    <>
      <HelpButton />
      <ThemeSwitcher />
      <Link href="/auth/signin">
        <Button variant="outline" size="sm" className="hidden sm:inline-flex">
          Log In
        </Button>
      </Link>
      <Link href="/auth/signup">
        <Button size="sm">Sign Up</Button>
      </Link>
    </>
  );

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Header/Navigation */}
      <ResponsiveNavigation
        items={defaultNavigationItems}
        actions={navigationActions}
      />

      <main className="flex-1">
        {/* Hero Section */}
        <ResponsiveSection spacing="lg" className="relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
          <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/10 via-transparent to-transparent" />

          <div className="relative">
            <ResponsiveStack gap="lg" align="center" className="text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="space-y-6"
              >
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Badge
                  variant="outline"
                  className="px-4 py-2 text-sm font-medium"
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  <span>Introducing TutorAI</span>
                </Badge>
              </motion.div>

                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                  className={textClasses.heading.h1}
                >
                  AI-Powered Browser Extension{" "}
                  <br className="hidden sm:inline" />
                  <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                    Tutoring System
                  </span>
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="max-w-2xl text-muted-foreground text-base sm:text-lg md:text-xl leading-relaxed"
                >
                  Real-time AI explanations, voice synthesis, and interactive
                  guidance for any webpage. Transform how you learn and navigate
                  the web with intelligent assistance.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                  className="flex flex-col sm:flex-row items-center justify-center gap-4"
                >
                  <Link href="/auth/signup">
                    <Button size="lg" className="gap-2 w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-6 text-base sm:text-lg">
                      <Download className="h-4 w-4 sm:h-5 sm:w-5" />
                      Get Started Free
                    </Button>
                  </Link>
                  <Button
                    size="lg"
                    variant="outline"
                    className="gap-2 w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-6 text-base sm:text-lg"
                  >
                    <Play className="h-4 w-4 sm:h-5 sm:w-5" />
                    Watch Demo
                  </Button>
                </motion.div>

                {/* Social Proof */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6 text-sm text-muted-foreground"
                >
                  <div className="flex items-center gap-1">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className="h-4 w-4 fill-yellow-400 text-yellow-400"
                        />
                      ))}
                    </div>
                    <span className="ml-2">4.9/5 from 2,000+ users</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    <span>12,000+ active learners</span>
                  </div>
                </motion.div>
              </motion.div>

              {/* Extension Preview */}
              <motion.div
                initial={{ opacity: 0, y: 40 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, delay: 0.7 }}
                className="relative w-full max-w-6xl mx-auto"
                data-testid="extension-demo"
              >
                <div className="relative overflow-hidden rounded-xl sm:rounded-2xl border bg-background shadow-xl sm:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-t from-background/50 to-transparent z-10" />
                  <ExtensionDemo />
                </div>
                {/* Floating elements - hidden on mobile */}
                <div className="hidden sm:block absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full blur-xl" />
                <div className="hidden sm:block absolute -bottom-4 -left-4 w-32 h-32 bg-secondary/10 rounded-full blur-xl" />
              </motion.div>
            </ResponsiveStack>
          </div>
        </ResponsiveSection>

        {/* Features Section */}
        <ResponsiveSection id="features" background="muted" spacing="lg">
          <ResponsiveStack gap="lg">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center space-y-4"
            >
              <h2 className={textClasses.heading.h2}>
                Powerful Features
              </h2>
              <p className="max-w-2xl mx-auto text-muted-foreground text-base sm:text-lg md:text-xl leading-relaxed">
                Our AI-powered tutoring system comes with everything you need to
                enhance learning and productivity.
              </p>
            </motion.div>

            <FeatureShowcase />

            <ResponsiveGrid cols={3} gap="lg">
              {[
                {
                  icon: Sparkles,
                  title: "AI Integration",
                  description:
                    "Multiple AI providers with context-aware explanations",
                  content:
                    "Seamlessly connect with OpenAI, Anthropic, and Google AI for intelligent, context-aware explanations of any webpage element.",
                  color: "text-blue-500",
                },
                {
                  icon: Headphones,
                  title: "Voice Capabilities",
                  description: "Premium text-to-speech and voice commands",
                  content:
                    "Experience natural voice interactions with ElevenLabs integration, voice command recognition, and customizable voice settings.",
                  color: "text-green-500",
                },
                {
                  icon: Globe,
                  title: "Enterprise Ready",
                  description: "Multi-language support and white-label options",
                  content:
                    "Deploy at scale with SSO integration, white-label customization, multi-language support, and comprehensive analytics.",
                  color: "text-purple-500",
                },
              ].map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Card className="h-full hover:shadow-lg transition-all duration-300 group">
                      <CardHeader className="pb-4">
                        <Icon
                          className={`h-6 w-6 sm:h-8 sm:w-8 ${feature.color} mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300`}
                        />
                        <CardTitle className="text-lg sm:text-xl">
                          {feature.title}
                        </CardTitle>
                        <CardDescription className="text-sm sm:text-base">
                          {feature.description}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pb-4">
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {feature.content}
                        </p>
                      </CardContent>
                      <CardFooter>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="gap-2 group-hover:gap-3 transition-all text-sm"
                        >
                          Learn more <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  </motion.div>
                );
              })}
            </ResponsiveGrid>
          </ResponsiveStack>
        </ResponsiveSection>

        {/* Testimonials Section */}
        <ResponsiveSection spacing="lg">
          <ResponsiveStack gap="lg">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center space-y-4"
            >
              <h2 className={textClasses.heading.h2}>
                Loved by Learners Worldwide
              </h2>
              <p className="max-w-2xl mx-auto text-muted-foreground text-base sm:text-lg md:text-xl leading-relaxed">
                See what our users are saying about their learning experience.
              </p>
            </motion.div>

            <ResponsiveGrid cols={3} gap="lg">
              {[
                {
                  name: "Sarah Chen",
                  role: "UX Designer",
                  content:
                    "TutorAI has completely transformed how I learn new design tools. The AI explanations are incredibly helpful and the voice guidance makes complex workflows so much easier to follow.",
                  avatar: "SC",
                },
                {
                  name: "Marcus Rodriguez",
                  role: "Software Developer",
                  content:
                    "As a developer, I'm constantly learning new frameworks. TutorAI's contextual explanations have cut my learning time in half. It's like having a personal tutor for every website.",
                  avatar: "MR",
                },
                {
                  name: "Emily Watson",
                  role: "Product Manager",
                  content:
                    "The enterprise features are outstanding. We've deployed TutorAI across our team and the analytics help us track learning progress. The white-label option fits perfectly with our brand.",
                  avatar: "EW",
                },
              ].map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full">
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex mb-3 sm:mb-4">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className="h-4 w-4 fill-yellow-400 text-yellow-400"
                          />
                        ))}
                      </div>
                      <p className="text-muted-foreground mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
                        &quot;{testimonial.content}&quot;
                      </p>
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-primary/10 flex items-center justify-center font-medium text-primary text-sm sm:text-base">
                          {testimonial.avatar}
                        </div>
                        <div>
                          <p className="font-medium text-sm sm:text-base">{testimonial.name}</p>
                          <p className="text-xs sm:text-sm text-muted-foreground">
                            {testimonial.role}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </ResponsiveGrid>
          </ResponsiveStack>
        </ResponsiveSection>

        {/* Demo Section */}
        <ResponsiveSection id="demo" background="muted" spacing="lg">
          <ResponsiveStack gap="lg">
            <div className="text-center space-y-4">
              <h2 className={textClasses.heading.h2}>
                See It In Action
              </h2>
              <p className="max-w-2xl mx-auto text-muted-foreground text-base sm:text-lg md:text-xl leading-relaxed">
                Experience how our browser extension transforms any webpage into
                an interactive learning environment.
              </p>
            </div>

            <Tabs defaultValue="overlay" className="w-full max-w-4xl mx-auto">
              <TabsList className="grid w-full grid-cols-1 sm:grid-cols-3 h-auto sm:h-10">
                <TabsTrigger value="overlay" className="text-xs sm:text-sm">
                  Interactive Overlay
                </TabsTrigger>
                <TabsTrigger value="voice" className="text-xs sm:text-sm">
                  Voice Interaction
                </TabsTrigger>
                <TabsTrigger value="tutorial" className="text-xs sm:text-sm">
                  Step-by-Step Tutorial
                </TabsTrigger>
              </TabsList>
              <TabsContent value="overlay" className="mt-4 sm:mt-6">
                <Card>
                  <CardContent className="p-4 sm:p-6">
                    <div className="aspect-video rounded-lg overflow-hidden border bg-background">
                      <div className="w-full h-full flex items-center justify-center bg-muted">
                        <img
                          src="https://images.unsplash.com/photo-1579546929518-9e396f3cc809?w=800&q=80"
                          alt="Interactive overlay demonstration"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    <div className="mt-3 sm:mt-4">
                      <h3 className="text-base sm:text-lg font-medium">
                        Smart Element Detection
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2">
                        Our extension intelligently identifies and highlights
                        important elements on any webpage, providing
                        context-aware explanations and guidance.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="voice" className="mt-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="aspect-video rounded-lg overflow-hidden border bg-background">
                      <div className="w-full h-full flex items-center justify-center bg-muted">
                        <img
                          src="https://images.unsplash.com/photo-1590012314607-cda9d9b699ae?w=800&q=80"
                          alt="Voice interaction demonstration"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-lg font-medium">
                        Natural Voice Interaction
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2">
                        Control the extension with voice commands and receive
                        natural-sounding explanations through premium
                        text-to-speech technology.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              <TabsContent value="tutorial" className="mt-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="aspect-video rounded-lg overflow-hidden border bg-background">
                      <div className="w-full h-full flex items-center justify-center bg-muted">
                        <img
                          src="https://images.unsplash.com/photo-1606857521015-7f9fcf423740?w=800&q=80"
                          alt="Step-by-step tutorial demonstration"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    <div className="mt-4">
                      <h3 className="text-lg font-medium">
                        Guided Learning Experience
                      </h3>
                      <p className="text-sm text-muted-foreground mt-2">
                        Follow step-by-step tutorials that guide you through
                        complex processes on any website with interactive
                        navigation and AI-powered explanations.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </ResponsiveStack>
        </ResponsiveSection>

        {/* Dashboard Preview Section */}
        <ResponsiveSection id="dashboard" spacing="lg">
          <ResponsiveStack gap="lg">
            <div className="text-center space-y-4">
              <h2 className={textClasses.heading.h2}>
                Powerful Admin Dashboard
              </h2>
              <p className="max-w-2xl mx-auto text-muted-foreground text-base sm:text-lg md:text-xl leading-relaxed">
                Manage tutorials, track analytics, and configure AI providers
                through our intuitive admin interface.
              </p>
            </div>

            <div className="relative mx-auto max-w-5xl overflow-hidden rounded-lg sm:rounded-xl border bg-background shadow-lg sm:shadow-xl">
              <DashboardPreview />
            </div>

            <ResponsiveGrid cols={4} gap="md">
              <Card>
                <CardHeader className="pb-3 sm:pb-2">
                  <CardTitle className="text-base sm:text-lg">Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Track usage patterns and measure tutorial effectiveness with
                    comprehensive analytics.
                  </p>
                </CardContent>
              </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Tutorial Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Create, edit, and deploy interactive tutorials for any website
                  or application.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">AI Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Configure multiple AI providers and customize response
                  parameters for optimal results.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">User Management</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Manage user access, roles, and permissions with
                  enterprise-grade controls.
                </p>
              </CardContent>
            </Card>
            </ResponsiveGrid>
          </ResponsiveStack>
        </ResponsiveSection>

        {/* Pricing Section */}
        <ResponsiveSection id="pricing" background="muted" spacing="lg">
          <ResponsiveStack gap="lg">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center space-y-4"
            >
              <h2 className={textClasses.heading.h2}>
                Simple, Transparent Pricing
              </h2>
              <p className="max-w-2xl mx-auto text-muted-foreground text-base sm:text-lg md:text-xl leading-relaxed">
                Choose the plan that's right for you or your organization.
              </p>
            </motion.div>

            <div className="max-w-5xl mx-auto w-full">
              <ResponsiveGrid cols={3} gap="lg">
              {[
                {
                  name: "Basic",
                  price: "$9",
                  period: "/month",
                  description: "Perfect for individual learners",
                  features: [
                    "AI-powered explanations",
                    "Basic voice capabilities",
                    "100 AI requests per day",
                    "Single browser support",
                    "Email support",
                  ],
                  popular: false,
                  cta: "Get Started",
                },
                {
                  name: "Pro",
                  price: "$29",
                  period: "/month",
                  description: "Best for professionals and teams",
                  features: [
                    "Everything in Basic",
                    "Premium voice synthesis",
                    "Unlimited AI requests",
                    "Multi-browser support",
                    "Custom tutorial creation",
                    "Priority support",
                  ],
                  popular: true,
                  cta: "Get Started",
                },
                {
                  name: "Enterprise",
                  price: "Custom",
                  period: "",
                  description: "For large organizations",
                  features: [
                    "Everything in Pro",
                    "SSO integration",
                    "White-label options",
                    "Advanced analytics",
                    "Dedicated support",
                    "Custom integrations",
                  ],
                  popular: false,
                  cta: "Contact Sales",
                },
              ].map((plan, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <Card
                    className={`relative h-full ${plan.popular ? "border-primary shadow-lg sm:scale-105" : "border-muted"}`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="px-2 sm:px-3 py-1 text-xs sm:text-sm">Most Popular</Badge>
                      </div>
                    )}
                    <CardHeader className="text-center pb-6 sm:pb-8">
                      <CardTitle className="text-xl sm:text-2xl">{plan.name}</CardTitle>
                      <div className="mt-3 sm:mt-4">
                        <span className="text-3xl sm:text-4xl font-bold">{plan.price}</span>
                        <span className="text-muted-foreground ml-1 text-sm sm:text-base">
                          {plan.period}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        {plan.description}
                      </p>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <ul className="space-y-3">
                        {plan.features.map((feature, featureIndex) => (
                          <li
                            key={featureIndex}
                            className="flex items-center text-sm"
                          >
                            <CheckCircle className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter className="pt-8">
                      <Link
                        href={plan.name === "Enterprise" ? "#" : "/auth/signup"}
                        className="w-full"
                      >
                        <Button
                          className="w-full"
                          variant={plan.popular ? "default" : "outline"}
                        >
                          {plan.cta}
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
              </ResponsiveGrid>
            </div>
          </ResponsiveStack>
        </ResponsiveSection>

        {/* CTA Section */}
        <ResponsiveSection spacing="lg">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative overflow-hidden rounded-xl sm:rounded-2xl bg-gradient-to-br from-primary via-primary to-primary/80 p-6 sm:p-8 md:p-12 lg:p-16 text-primary-foreground"
          >
              {/* Background Pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-white/10 via-transparent to-transparent" />
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full blur-3xl" />
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full blur-2xl" />

            <div className="relative mx-auto max-w-4xl space-y-6 sm:space-y-8 text-center">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tighter">
                Ready to transform your learning experience?
              </h2>
              <p className="text-primary-foreground/90 text-base sm:text-lg md:text-xl leading-relaxed max-w-2xl mx-auto">
                Join thousands of learners who are already using TutorAI to
                master new skills faster than ever before.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link href="/auth/signup">
                  <Button
                    size="lg"
                    variant="secondary"
                    className="gap-2 w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-6 text-base sm:text-lg"
                  >
                    <Download className="h-4 w-4 sm:h-5 sm:w-5" />
                    Start Learning Today
                  </Button>
                </Link>
                <Button
                  size="lg"
                  variant="outline"
                  className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10 gap-2 w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-6 text-base sm:text-lg"
                >
                  <Settings className="h-4 w-4 sm:h-5 sm:w-5" />
                  View Documentation
                </Button>
              </div>

              {/* Trust indicators */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 pt-6 sm:pt-8 text-primary-foreground/70">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="text-sm">Enterprise Security</span>
                </div>
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="text-sm">Instant Setup</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="text-sm">24/7 Support</span>
                </div>
              </div>
            </div>
          </motion.div>
        </ResponsiveSection>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/50">
        <ResponsiveContainer className="py-8 sm:py-12">
          <ResponsiveStack gap="lg">
            <ResponsiveGrid cols={4} gap="lg" className="lg:grid-cols-5">
              <div className="col-span-2 lg:col-span-2">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  <span className="text-lg font-bold">TutorAI</span>
                </div>
                <p className="mt-2 text-sm text-muted-foreground max-w-xs">
                  AI-powered browser extension tutoring system with real-time
                  explanations and voice synthesis.
                </p>
              </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Product</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Features
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Pricing
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Documentation
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Changelog
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Company</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Blog
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Careers
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-3">Legal</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Cookie Policy
                  </Link>
                </li>
              </ul>
            </div>
            </ResponsiveGrid>

            <div className="border-t pt-6 sm:pt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-sm text-muted-foreground">
                © 2023 TutorAI. All rights reserved.
              </p>
              <div className="flex items-center gap-4">
              <Link
                href="#"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-twitter"
                >
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                </svg>
                <span className="sr-only">Twitter</span>
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-github"
                >
                  <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path>
                  <path d="M9 18c-4.51 2-5-2-7-2"></path>
                </svg>
                <span className="sr-only">GitHub</span>
              </Link>
              <Link
                href="#"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="lucide lucide-linkedin"
                >
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                  <rect width="4" height="12" x="2" y="9"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
                <span className="sr-only">LinkedIn</span>
              </Link>
              </div>
            </div>
          </ResponsiveStack>
        </ResponsiveContainer>
      </footer>
    </div>
  );
}
