"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ThemeSwitcher } from "@/components/theme-switcher";
import {
  BookOpen,
  Search,
  Filter,
  Clock,
  Users,
  Star,
  Sparkles,
  ArrowLeft,
} from "lucide-react";
import { Tutorial } from "@/models/Tutorial";

interface TutorialWithProgress extends Tutorial {
  progress?: number;
  isCompleted?: boolean;
}

export default function TutorialsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [tutorials, setTutorials] = useState<TutorialWithProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [difficultyFilter, setDifficultyFilter] = useState("all");

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/signin");
    } else if (status === "authenticated") {
      fetchTutorials();
    }
  }, [status, router]);

  const fetchTutorials = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        ...(categoryFilter !== "all" && { category: categoryFilter }),
        ...(difficultyFilter !== "all" && { difficulty: difficultyFilter }),
      });

      const response = await fetch(`/api/tutorials?${params}`);
      if (response.ok) {
        const data = await response.json();
        setTutorials(data.tutorials || []);
      }
    } catch (error) {
      console.error("Error fetching tutorials:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredTutorials = tutorials.filter(
    (tutorial) =>
      tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tutorial.description?.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  if (status === "loading" || isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tutorials...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
            <div className="flex items-center gap-2">
              <Sparkles className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">TutorAI</span>
            </div>
          </div>
          <ThemeSwitcher />
        </div>
      </header>

      {/* Main Content */}
      <div className="container py-8">
        <div className="flex flex-col gap-8">
          {/* Page Header */}
          <div className="flex flex-col gap-4">
            <h1 className="text-3xl font-bold tracking-tight">Tutorials</h1>
            <p className="text-muted-foreground">
              Discover interactive tutorials to enhance your web navigation
              skills with AI-powered guidance.
            </p>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tutorials..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="navigation">Navigation</SelectItem>
                <SelectItem value="forms">Forms</SelectItem>
                <SelectItem value="ecommerce">E-commerce</SelectItem>
                <SelectItem value="productivity">Productivity</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={difficultyFilter}
              onValueChange={setDifficultyFilter}
            >
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tutorials Grid */}
          {filteredTutorials.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No tutorials found</h3>
              <p className="text-muted-foreground">
                {searchQuery ||
                categoryFilter !== "all" ||
                difficultyFilter !== "all"
                  ? "Try adjusting your search or filters."
                  : "No tutorials are available at the moment."}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTutorials.map((tutorial) => (
                <Card
                  key={tutorial.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-2">
                          {tutorial.title}
                        </CardTitle>
                        <CardDescription className="line-clamp-2">
                          {tutorial.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-3">
                      <Badge
                        variant={
                          tutorial.metadata.difficulty === "beginner"
                            ? "secondary"
                            : tutorial.metadata.difficulty === "intermediate"
                              ? "default"
                              : "destructive"
                        }
                      >
                        {tutorial.metadata.difficulty}
                      </Badge>
                      <Badge variant="outline">
                        {tutorial.metadata.category}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {tutorial.progress !== undefined && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Progress</span>
                            <span>{tutorial.progress}%</span>
                          </div>
                          <Progress value={tutorial.progress} className="h-2" />
                        </div>
                      )}

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{tutorial.metadata.estimatedTime} min</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>{tutorial.metadata.analytics.views}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4" />
                          <span>
                            {tutorial.metadata.analytics.averageRating.toFixed(
                              1,
                            )}
                          </span>
                        </div>
                      </div>

                      <Link href={`/tutorials/${tutorial.id}`}>
                        <Button className="w-full">
                          {tutorial.isCompleted ? "Review" : "Start Tutorial"}
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
