/**
 * TutorAI Performance Monitor
 * Collects performance metrics, monitors resource usage, and provides optimization suggestions
 */

class TutorAIPerformanceMonitor {
  constructor() {
    this.metrics = {
      memory: [],
      responseTime: [],
      errors: [],
      cacheHits: 0,
      cacheMisses: 0,
      apiCalls: 0,
      contentScriptInjections: 0,
      tutorialSessions: 0
    };
    
    this.thresholds = {
      memoryWarning: 50 * 1024 * 1024, // 50MB
      memoryCritical: 100 * 1024 * 1024, // 100MB
      responseTimeWarning: 2000, // 2 seconds
      responseTimeCritical: 5000, // 5 seconds
      errorRateWarning: 0.05, // 5%
      errorRateCritical: 0.1 // 10%
    };
    
    this.monitoringInterval = null;
    this.performanceObserver = null;
    this.startTime = Date.now();
    this.isMonitoring = false;
    
    this.init();
  }

  async init() {
    // Start monitoring
    this.startMonitoring();
    
    // Set up performance observer
    this.setupPerformanceObserver();
    
    // Set up memory monitoring
    this.setupMemoryMonitoring();
    
    console.log('TutorAI: Performance Monitor initialized');
  }

  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    // Monitor every 30 seconds
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, 30000);
    
    // Initial collection
    this.collectMetrics();
  }

  stopMonitoring() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
  }

  setupPerformanceObserver() {
    if (!window.PerformanceObserver) return;
    
    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach(entry => {
          if (entry.entryType === 'measure') {
            this.recordResponseTime(entry.name, entry.duration);
          } else if (entry.entryType === 'navigation') {
            this.recordPageLoad(entry.duration);
          }
        });
      });
      
      this.performanceObserver.observe({ 
        entryTypes: ['measure', 'navigation', 'resource'] 
      });
      
    } catch (error) {
      console.warn('TutorAI: Performance Observer not supported:', error);
    }
  }

  setupMemoryMonitoring() {
    // Monitor memory usage if available
    if (chrome.system && chrome.system.memory) {
      setInterval(async () => {
        try {
          const memInfo = await chrome.system.memory.getInfo();
          this.recordMemoryUsage(memInfo);
        } catch (error) {
          console.warn('TutorAI: Memory monitoring failed:', error);
        }
      }, 60000); // Every minute
    }
  }

  async collectMetrics() {
    try {
      // Collect extension-specific metrics
      await this.collectExtensionMetrics();
      
      // Collect browser performance metrics
      this.collectBrowserMetrics();
      
      // Check for performance issues
      this.checkPerformanceThresholds();
      
      // Clean old metrics (keep last 100 entries)
      this.cleanOldMetrics();
      
    } catch (error) {
      console.error('TutorAI: Failed to collect metrics:', error);
      this.recordError('metrics_collection', error);
    }
  }

  async collectExtensionMetrics() {
    // Get storage usage
    const storageUsage = await this.getStorageUsage();
    
    // Get active tabs count
    const tabs = await chrome.tabs.query({});
    const activeTabs = tabs.filter(tab => 
      !tab.url.startsWith('chrome://') && 
      !tab.url.startsWith('chrome-extension://')
    );
    
    // Record metrics
    this.recordMetric('storage_usage', storageUsage);
    this.recordMetric('active_tabs', activeTabs.length);
    this.recordMetric('total_tabs', tabs.length);
    
    // Get cache statistics
    const cacheStats = this.getCacheStatistics();
    this.recordMetric('cache_hit_rate', cacheStats.hitRate);
    this.recordMetric('cache_size', cacheStats.size);
  }

  collectBrowserMetrics() {
    if (!performance) return;
    
    // Get timing information
    const timing = performance.timing;
    if (timing) {
      const pageLoadTime = timing.loadEventEnd - timing.navigationStart;
      if (pageLoadTime > 0) {
        this.recordMetric('page_load_time', pageLoadTime);
      }
    }
    
    // Get memory information (if available)
    if (performance.memory) {
      this.recordMemoryUsage({
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize,
        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
      });
    }
  }

  async getStorageUsage() {
    try {
      const syncUsage = await chrome.storage.sync.getBytesInUse();
      const localUsage = await chrome.storage.local.getBytesInUse();
      
      return {
        sync: syncUsage,
        local: localUsage,
        total: syncUsage + localUsage
      };
    } catch (error) {
      console.warn('TutorAI: Failed to get storage usage:', error);
      return { sync: 0, local: 0, total: 0 };
    }
  }

  getCacheStatistics() {
    const totalRequests = this.metrics.cacheHits + this.metrics.cacheMisses;
    const hitRate = totalRequests > 0 ? this.metrics.cacheHits / totalRequests : 0;
    
    return {
      hits: this.metrics.cacheHits,
      misses: this.metrics.cacheMisses,
      hitRate: hitRate,
      size: this.estimateCacheSize()
    };
  }

  estimateCacheSize() {
    // Estimate cache size based on stored data
    // This is a rough estimate
    return this.metrics.cacheHits * 1024; // Assume 1KB per cached item
  }

  recordMetric(name, value) {
    const timestamp = Date.now();
    
    if (!this.metrics[name]) {
      this.metrics[name] = [];
    }
    
    this.metrics[name].push({
      value: value,
      timestamp: timestamp
    });
    
    // Keep only last 100 entries
    if (this.metrics[name].length > 100) {
      this.metrics[name] = this.metrics[name].slice(-100);
    }
  }

  recordResponseTime(operation, duration) {
    this.recordMetric('response_time', {
      operation: operation,
      duration: duration
    });
    
    // Check for slow operations
    if (duration > this.thresholds.responseTimeWarning) {
      this.recordPerformanceAlert('slow_operation', {
        operation: operation,
        duration: duration,
        threshold: this.thresholds.responseTimeWarning
      });
    }
  }

  recordMemoryUsage(memInfo) {
    this.recordMetric('memory_usage', memInfo);
    
    // Check memory thresholds
    const usedMemory = memInfo.usedJSHeapSize || memInfo.used || 0;
    
    if (usedMemory > this.thresholds.memoryCritical) {
      this.recordPerformanceAlert('memory_critical', {
        used: usedMemory,
        threshold: this.thresholds.memoryCritical
      });
    } else if (usedMemory > this.thresholds.memoryWarning) {
      this.recordPerformanceAlert('memory_warning', {
        used: usedMemory,
        threshold: this.thresholds.memoryWarning
      });
    }
  }

  recordError(type, error) {
    this.recordMetric('errors', {
      type: type,
      message: error.message || error,
      stack: error.stack,
      timestamp: Date.now()
    });
  }

  recordPerformanceAlert(type, data) {
    const alert = {
      type: type,
      data: data,
      timestamp: Date.now(),
      severity: this.getAlertSeverity(type)
    };
    
    if (!this.metrics.alerts) {
      this.metrics.alerts = [];
    }
    
    this.metrics.alerts.push(alert);
    
    // Keep only last 50 alerts
    if (this.metrics.alerts.length > 50) {
      this.metrics.alerts = this.metrics.alerts.slice(-50);
    }
    
    // Notify about critical alerts
    if (alert.severity === 'critical') {
      this.notifyPerformanceIssue(alert);
    }
  }

  getAlertSeverity(type) {
    const criticalTypes = ['memory_critical', 'error_rate_critical'];
    const warningTypes = ['memory_warning', 'slow_operation', 'error_rate_warning'];
    
    if (criticalTypes.includes(type)) return 'critical';
    if (warningTypes.includes(type)) return 'warning';
    return 'info';
  }

  checkPerformanceThresholds() {
    // Check error rate
    const recentErrors = this.getRecentMetrics('errors', 300000); // Last 5 minutes
    const totalOperations = this.metrics.apiCalls + this.metrics.contentScriptInjections;
    
    if (totalOperations > 0) {
      const errorRate = recentErrors.length / totalOperations;
      
      if (errorRate > this.thresholds.errorRateCritical) {
        this.recordPerformanceAlert('error_rate_critical', {
          rate: errorRate,
          errors: recentErrors.length,
          operations: totalOperations
        });
      } else if (errorRate > this.thresholds.errorRateWarning) {
        this.recordPerformanceAlert('error_rate_warning', {
          rate: errorRate,
          errors: recentErrors.length,
          operations: totalOperations
        });
      }
    }
  }

  getRecentMetrics(metricName, timeWindow = 300000) {
    if (!this.metrics[metricName]) return [];
    
    const cutoff = Date.now() - timeWindow;
    return this.metrics[metricName].filter(metric => 
      metric.timestamp > cutoff
    );
  }

  cleanOldMetrics() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours
    
    Object.keys(this.metrics).forEach(key => {
      if (Array.isArray(this.metrics[key])) {
        this.metrics[key] = this.metrics[key].filter(metric => 
          !metric.timestamp || metric.timestamp > cutoff
        );
      }
    });
  }

  async notifyPerformanceIssue(alert) {
    try {
      // Send to background script for handling
      if (chrome.runtime) {
        chrome.runtime.sendMessage({
          type: 'PERFORMANCE_ALERT',
          alert: alert
        });
      }
    } catch (error) {
      console.error('TutorAI: Failed to notify performance issue:', error);
    }
  }

  // Public API methods
  getMetrics() {
    return {
      ...this.metrics,
      uptime: Date.now() - this.startTime,
      isMonitoring: this.isMonitoring
    };
  }

  getPerformanceSummary() {
    const summary = {
      uptime: Date.now() - this.startTime,
      totalErrors: this.metrics.errors?.length || 0,
      totalAlerts: this.metrics.alerts?.length || 0,
      cacheHitRate: this.getCacheStatistics().hitRate,
      averageResponseTime: this.getAverageResponseTime(),
      memoryUsage: this.getLatestMemoryUsage(),
      recommendations: this.getOptimizationRecommendations()
    };
    
    return summary;
  }

  getAverageResponseTime() {
    const responseTimes = this.getRecentMetrics('response_time', 600000); // Last 10 minutes
    if (responseTimes.length === 0) return 0;
    
    const total = responseTimes.reduce((sum, metric) => 
      sum + (metric.value.duration || 0), 0
    );
    
    return total / responseTimes.length;
  }

  getLatestMemoryUsage() {
    const memoryMetrics = this.metrics.memory_usage;
    if (!memoryMetrics || memoryMetrics.length === 0) return null;
    
    return memoryMetrics[memoryMetrics.length - 1].value;
  }

  getOptimizationRecommendations() {
    const recommendations = [];
    const summary = this.getPerformanceSummary();
    
    // Memory recommendations
    if (summary.memoryUsage && summary.memoryUsage.usedJSHeapSize > this.thresholds.memoryWarning) {
      recommendations.push({
        type: 'memory',
        severity: 'warning',
        message: 'High memory usage detected. Consider clearing cache or reducing concurrent operations.',
        action: 'clear_cache'
      });
    }
    
    // Response time recommendations
    if (summary.averageResponseTime > this.thresholds.responseTimeWarning) {
      recommendations.push({
        type: 'performance',
        severity: 'warning',
        message: 'Slow response times detected. Consider enabling preload content or reducing tutorial delay.',
        action: 'optimize_settings'
      });
    }
    
    // Cache recommendations
    if (summary.cacheHitRate < 0.7) {
      recommendations.push({
        type: 'cache',
        severity: 'info',
        message: 'Low cache hit rate. Consider increasing cache size or enabling preload content.',
        action: 'improve_caching'
      });
    }
    
    // Error rate recommendations
    if (summary.totalErrors > 10) {
      recommendations.push({
        type: 'reliability',
        severity: 'warning',
        message: 'High error rate detected. Check network connectivity and server status.',
        action: 'check_connectivity'
      });
    }
    
    return recommendations;
  }

  // Increment counters
  incrementCacheHit() {
    this.metrics.cacheHits++;
  }

  incrementCacheMiss() {
    this.metrics.cacheMisses++;
  }

  incrementApiCall() {
    this.metrics.apiCalls++;
  }

  incrementContentScriptInjection() {
    this.metrics.contentScriptInjections++;
  }

  incrementTutorialSession() {
    this.metrics.tutorialSessions++;
  }

  // Cleanup
  destroy() {
    this.stopMonitoring();
  }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TutorAIPerformanceMonitor;
} else {
  window.TutorAIPerformanceMonitor = TutorAIPerformanceMonitor;
}
