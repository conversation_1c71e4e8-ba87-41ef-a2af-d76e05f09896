"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useOnboarding } from './OnboardingProvider';
import { cn } from '@/lib/utils';
import { 
  Sparkles, 
  Play, 
  ArrowRight, 
  CheckCircle,
  Clock,
  Users,
  Zap,
  Shield,
  X
} from 'lucide-react';

interface WelcomeScreenProps {
  onClose?: () => void;
  onStartTour?: () => void;
  className?: string;
}

export function WelcomeScreen({ onClose, onStartTour, className }: WelcomeScreenProps) {
  const { startFlow, isFlowCompleted, settings } = useOnboarding();
  const [selectedFlows, setSelectedFlows] = useState<string[]>(['main-tour']);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const availableFlows = [
    {
      id: 'main-tour',
      name: 'Main Tour',
      description: 'Get familiar with the core features and interface',
      duration: '3-5 minutes',
      difficulty: 'Beginner',
      icon: Play,
      recommended: true,
    },
    {
      id: 'ai-features',
      name: 'AI Features',
      description: 'Learn how to use AI-powered explanations and voice synthesis',
      duration: '2-3 minutes',
      difficulty: 'Beginner',
      icon: Zap,
      recommended: true,
    },
    {
      id: 'dashboard-tour',
      name: 'Dashboard Tour',
      description: 'Explore analytics, settings, and management features',
      duration: '4-6 minutes',
      difficulty: 'Intermediate',
      icon: Users,
      recommended: false,
    },
    {
      id: 'advanced-features',
      name: 'Advanced Features',
      description: 'Master advanced customization and integration options',
      duration: '5-8 minutes',
      difficulty: 'Advanced',
      icon: Shield,
      recommended: false,
    },
  ];

  const handleFlowToggle = (flowId: string) => {
    setSelectedFlows(prev => 
      prev.includes(flowId) 
        ? prev.filter(id => id !== flowId)
        : [...prev, flowId]
    );
  };

  const handleStartSelected = () => {
    if (selectedFlows.length > 0) {
      // Start the first selected flow
      startFlow(selectedFlows[0]);
      onStartTour?.();
    }
  };

  const handleSkipOnboarding = () => {
    onClose?.();
  };

  const features = [
    {
      icon: Sparkles,
      title: 'AI-Powered Learning',
      description: 'Get intelligent explanations for any webpage content',
    },
    {
      icon: Zap,
      title: 'Voice Synthesis',
      description: 'Listen to explanations with natural voice synthesis',
    },
    {
      icon: Users,
      title: 'Progress Tracking',
      description: 'Monitor your learning progress and achievements',
    },
    {
      icon: Shield,
      title: 'Privacy First',
      description: 'Your data stays secure with enterprise-grade protection',
    },
  ];

  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm",
      className
    )}>
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="w-full max-w-4xl mx-4"
      >
        <Card className="shadow-2xl border-primary/20">
          <CardHeader className="text-center pb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Sparkles className="h-8 w-8 text-primary" />
                <span className="text-2xl font-bold">TutorAI</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleSkipOnboarding}
                className="h-8 w-8"
                aria-label="Skip onboarding"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <CardTitle className="text-3xl mb-2">
              Welcome to TutorAI! 🎉
            </CardTitle>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Transform your browsing experience with AI-powered learning assistance. 
              Let's get you started with a quick tour of the features.
            </p>
          </CardHeader>

          <CardContent className="space-y-8">
            {/* Features Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3 p-4 rounded-lg bg-muted/30"
                >
                  <feature.icon className="h-6 w-6 text-primary mt-0.5" />
                  <div>
                    <h3 className="font-semibold mb-1">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Tour Selection */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Choose Your Learning Path</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  {showAdvanced ? 'Show Less' : 'Show All Options'}
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {availableFlows
                  .filter(flow => showAdvanced || flow.recommended)
                  .map((flow) => {
                    const isCompleted = isFlowCompleted(flow.id);
                    const isSelected = selectedFlows.includes(flow.id);
                    
                    return (
                      <motion.div
                        key={flow.id}
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className={cn(
                          "relative p-4 rounded-lg border-2 cursor-pointer transition-all",
                          isSelected 
                            ? "border-primary bg-primary/5" 
                            : "border-muted hover:border-primary/50",
                          isCompleted && "opacity-60"
                        )}
                        onClick={() => !isCompleted && handleFlowToggle(flow.id)}
                      >
                        <div className="flex items-start gap-3">
                          <Checkbox
                            checked={isSelected}
                            disabled={isCompleted}
                            className="mt-1"
                          />
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <flow.icon className="h-4 w-4" />
                              <h4 className="font-medium">{flow.name}</h4>
                              {flow.recommended && (
                                <Badge variant="secondary" className="text-xs">
                                  Recommended
                                </Badge>
                              )}
                              {isCompleted && (
                                <Badge variant="outline" className="text-xs gap-1">
                                  <CheckCircle className="h-3 w-3" />
                                  Completed
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {flow.description}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {flow.duration}
                              </div>
                              <Badge 
                                variant="outline" 
                                className={cn(
                                  "text-xs",
                                  flow.difficulty === 'Beginner' && "border-green-500 text-green-700",
                                  flow.difficulty === 'Intermediate' && "border-yellow-500 text-yellow-700",
                                  flow.difficulty === 'Advanced' && "border-red-500 text-red-700"
                                )}
                              >
                                {flow.difficulty}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t">
              <Button
                variant="ghost"
                onClick={handleSkipOnboarding}
                className="gap-2"
              >
                Skip for now
              </Button>

              <div className="flex items-center gap-3">
                <p className="text-sm text-muted-foreground">
                  {selectedFlows.length} tour{selectedFlows.length !== 1 ? 's' : ''} selected
                </p>
                <Button
                  onClick={handleStartSelected}
                  disabled={selectedFlows.length === 0}
                  className="gap-2"
                  size="lg"
                >
                  Start Learning
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
