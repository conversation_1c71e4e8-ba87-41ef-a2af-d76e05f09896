"use client";

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { OnboardingProvider, useOnboarding } from './OnboardingProvider';
import { OnboardingOverlay } from './OnboardingOverlay';
import { WelcomeScreen } from './WelcomeScreen';
import { allOnboardingFlows } from './flows';

interface OnboardingManagerProps {
  children: React.ReactNode;
  autoStart?: boolean;
  showWelcomeForNewUsers?: boolean;
}

function OnboardingContent({ 
  autoStart = true, 
  showWelcomeForNewUsers = true 
}: Omit<OnboardingManagerProps, 'children'>) {
  const { data: session, status } = useSession();
  const {
    registerFlow,
    startFlow,
    isActive,
    isFlowCompleted,
    settings,
  } = useOnboarding();

  const [showWelcome, setShowWelcome] = useState(false);
  const [hasCheckedOnboarding, setHasCheckedOnboarding] = useState(false);

  // Register all onboarding flows
  useEffect(() => {
    allOnboardingFlows.forEach(flow => {
      registerFlow(flow);
    });
  }, [registerFlow]);

  // Check if user needs onboarding
  useEffect(() => {
    if (status === 'loading' || hasCheckedOnboarding) return;

    const checkOnboardingStatus = () => {
      try {
        const hasSeenWelcome = localStorage.getItem('tutorai-welcome-seen');
        const hasCompletedMainTour = isFlowCompleted('main-tour');
        
        // Show welcome screen for new users who haven't seen it
        if (showWelcomeForNewUsers && !hasSeenWelcome && !hasCompletedMainTour) {
          setShowWelcome(true);
        } else if (autoStart && settings.autoStart && !hasCompletedMainTour) {
          // Auto-start main tour if enabled and not completed
          setTimeout(() => {
            startFlow('main-tour');
          }, 1000); // Small delay to ensure page is loaded
        }
        
        setHasCheckedOnboarding(true);
      } catch (error) {
        console.warn('Failed to check onboarding status:', error);
        setHasCheckedOnboarding(true);
      }
    };

    checkOnboardingStatus();
  }, [
    status, 
    autoStart, 
    showWelcomeForNewUsers, 
    hasCheckedOnboarding, 
    isFlowCompleted, 
    startFlow, 
    settings.autoStart
  ]);

  const handleWelcomeClose = () => {
    setShowWelcome(false);
    try {
      localStorage.setItem('tutorai-welcome-seen', 'true');
    } catch (error) {
      console.warn('Failed to save welcome status:', error);
    }
  };

  const handleStartTour = () => {
    setShowWelcome(false);
    handleWelcomeClose();
  };

  return (
    <>
      {showWelcome && (
        <WelcomeScreen
          onClose={handleWelcomeClose}
          onStartTour={handleStartTour}
        />
      )}
      
      {isActive && <OnboardingOverlay />}
    </>
  );
}

export function OnboardingManager({ 
  children, 
  autoStart = true, 
  showWelcomeForNewUsers = true 
}: OnboardingManagerProps) {
  return (
    <OnboardingProvider>
      {children}
      <OnboardingContent 
        autoStart={autoStart}
        showWelcomeForNewUsers={showWelcomeForNewUsers}
      />
    </OnboardingProvider>
  );
}

// Hook for triggering onboarding from components
export function useOnboardingTrigger() {
  const { startFlow, isFlowCompleted, registerFlow } = useOnboarding();

  const triggerMainTour = () => {
    if (!isFlowCompleted('main-tour')) {
      startFlow('main-tour');
    }
  };

  const triggerAIFeaturesTour = () => {
    if (!isFlowCompleted('ai-features')) {
      startFlow('ai-features');
    }
  };

  const triggerDashboardTour = () => {
    if (!isFlowCompleted('dashboard-tour')) {
      startFlow('dashboard-tour');
    }
  };

  const triggerAdvancedFeaturesTour = () => {
    if (!isFlowCompleted('advanced-features')) {
      startFlow('advanced-features');
    }
  };

  const triggerQuickStart = () => {
    startFlow('quick-start');
  };

  const triggerCustomFlow = (flowId: string) => {
    startFlow(flowId);
  };

  return {
    triggerMainTour,
    triggerAIFeaturesTour,
    triggerDashboardTour,
    triggerAdvancedFeaturesTour,
    triggerQuickStart,
    triggerCustomFlow,
    isFlowCompleted,
  };
}

// Component for onboarding trigger buttons
interface OnboardingTriggerProps {
  flowId: string;
  children: React.ReactNode;
  variant?: 'button' | 'link' | 'badge';
  className?: string;
  disabled?: boolean;
}

export function OnboardingTrigger({ 
  flowId, 
  children, 
  variant = 'button',
  className,
  disabled = false
}: OnboardingTriggerProps) {
  const { startFlow, isFlowCompleted } = useOnboarding();
  const isCompleted = isFlowCompleted(flowId);

  const handleClick = () => {
    if (!disabled && !isCompleted) {
      startFlow(flowId);
    }
  };

  if (variant === 'link') {
    return (
      <button
        onClick={handleClick}
        disabled={disabled || isCompleted}
        className={`text-primary hover:underline disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
      >
        {children}
      </button>
    );
  }

  if (variant === 'badge') {
    return (
      <span
        onClick={handleClick}
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium cursor-pointer transition-colors ${
          isCompleted 
            ? 'bg-green-100 text-green-800' 
            : 'bg-primary/10 text-primary hover:bg-primary/20'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
      >
        {children}
      </span>
    );
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled || isCompleted}
      className={`px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${className}`}
    >
      {children}
    </button>
  );
}

// Progress indicator component
export function OnboardingProgress() {
  const { currentFlow, currentStepIndex } = useOnboarding();

  if (!currentFlow) return null;

  const progress = ((currentStepIndex + 1) / currentFlow.steps.length) * 100;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
      <div className="bg-background/80 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg border">
        <div className="flex items-center gap-3">
          <span className="text-sm font-medium">{currentFlow.name}</span>
          <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="text-xs text-muted-foreground">
            {currentStepIndex + 1}/{currentFlow.steps.length}
          </span>
        </div>
      </div>
    </div>
  );
}
