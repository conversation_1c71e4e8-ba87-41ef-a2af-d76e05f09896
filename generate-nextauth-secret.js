#!/usr/bin/env node

/**
 * Generate a secure NEXTAUTH_SECRET
 * This script generates a cryptographically secure random string for NextAuth.js
 */

const crypto = require('crypto');

function generateNextAuthSecret() {
  // Generate 32 random bytes and convert to base64
  const secret = crypto.randomBytes(32).toString('base64');
  
  console.log('🔐 Generated NEXTAUTH_SECRET:');
  console.log('');
  console.log(`NEXTAUTH_SECRET="${secret}"`);
  console.log('');
  console.log('📋 Instructions:');
  console.log('1. Copy the line above to your .env file');
  console.log('2. Replace any existing NEXTAUTH_SECRET value');
  console.log('3. Never commit this secret to version control');
  console.log('4. Use a different secret for each environment (dev/staging/prod)');
  console.log('');
  console.log('✅ This secret is 44 characters long (meets 32+ requirement)');
  
  return secret;
}

// Run if called directly
if (require.main === module) {
  generateNextAuthSecret();
}

module.exports = { generateNextAuthSecret };
