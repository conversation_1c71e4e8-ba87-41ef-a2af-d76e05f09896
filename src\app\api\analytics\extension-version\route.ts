/**
 * Extension Version Analytics API
 * Tracks extension version information and update patterns
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting for analytics
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 1000, // limit it to 1000 requests per minute
});

const VersionAnalyticsSchema = z.object({
  extensionId: z.string(),
  currentVersion: z.string(),
  latestVersion: z.string(),
  userAgent: z.string(),
  timestamp: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip ?? '127.0.0.1';
    const { success } = await limiter.check(20, ip); // 20 requests per minute per IP
    
    if (!success) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const validatedData = VersionAnalyticsSchema.parse(body);

    // Store version analytics
    await prisma.analytics.create({
      data: {
        userId: 'system', // System-generated event
        action: 'extension_version_check',
        metadata: {
          extensionId: validatedData.extensionId,
          currentVersion: validatedData.currentVersion,
          latestVersion: validatedData.latestVersion,
          userAgent: validatedData.userAgent,
          isOutdated: validatedData.currentVersion !== validatedData.latestVersion,
          timestamp: validatedData.timestamp,
        },
      },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Extension version analytics error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
