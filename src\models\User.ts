export interface User {
  id: string;
  email: string;
  name?: string;
  isActive: boolean;
  image?: string;
  role: "USER" | "ADMIN" | "MODERATOR";
  subscription?: string;
  settings: UserSettings;
  lastLoginAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSettings {
  language: string;
  voiceProvider: string;
  voiceSettings: {
    speed: number;
    pitch: number;
    voice: string;
  };
  aiProvider: string;
  tutorialPreferences: {
    autoStart: boolean;
    voiceCommands: boolean;
    difficulty: "beginner" | "intermediate" | "advanced";
  };
  accessibility: {
    highContrast: boolean;
    largeText: boolean;
    screenReader: boolean;
  };
}

export interface CreateUserData {
  email: string;
  name?: string;
  image?: string;
  role?: "USER" | "ADMIN" | "MODERATOR";
  subscription?: string;
  settings?: Partial<UserSettings>;
}

export interface UpdateUserData {
  name?: string;
  image?: string;
  settings?: Partial<UserSettings>;
}

export const defaultUserSettings: UserSettings = {
  language: "en",
  voiceProvider: "elevenlabs",
  voiceSettings: {
    speed: 1.0,
    pitch: 1.0,
    voice: "default",
  },
  aiProvider: "openai",
  tutorialPreferences: {
    autoStart: true,
    voiceCommands: true,
    difficulty: "beginner",
  },
  accessibility: {
    highContrast: false,
    largeText: false,
    screenReader: false,
  },
};
