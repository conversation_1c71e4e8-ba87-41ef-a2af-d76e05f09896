"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useOnboardingTrigger } from '@/components/onboarding/OnboardingManager';
import { cn } from '@/lib/utils';
import { 
  HelpCircle, 
  Play, 
  BookOpen, 
  Zap, 
  Users, 
  Shield,
  CheckCircle,
  RotateCcw
} from 'lucide-react';

interface HelpButtonProps {
  variant?: 'default' | 'floating' | 'compact';
  className?: string;
  showBadge?: boolean;
}

export function HelpButton({ 
  variant = 'default', 
  className,
  showBadge = true 
}: HelpButtonProps) {
  const {
    triggerMainTour,
    triggerAIFeaturesTour,
    triggerDashboardTour,
    triggerAdvancedFeaturesTour,
    triggerQuickStart,
    isFlowCompleted,
  } = useOnboardingTrigger();

  const [isOpen, setIsOpen] = useState(false);

  const tours = [
    {
      id: 'main-tour',
      name: 'Main Tour',
      description: 'Get familiar with core features',
      icon: Play,
      trigger: triggerMainTour,
      recommended: true,
    },
    {
      id: 'ai-features',
      name: 'AI Features',
      description: 'Learn about AI explanations and voice',
      icon: Zap,
      trigger: triggerAIFeaturesTour,
      recommended: true,
    },
    {
      id: 'dashboard-tour',
      name: 'Dashboard Tour',
      description: 'Explore analytics and settings',
      icon: Users,
      trigger: triggerDashboardTour,
      recommended: false,
    },
    {
      id: 'advanced-features',
      name: 'Advanced Features',
      description: 'Master customization options',
      icon: Shield,
      trigger: triggerAdvancedFeaturesTour,
      recommended: false,
    },
  ];

  const completedCount = tours.filter(tour => isFlowCompleted(tour.id)).length;
  const hasUncompletedRecommended = tours.some(tour => 
    tour.recommended && !isFlowCompleted(tour.id)
  );

  const buttonContent = (
    <>
      <HelpCircle className="h-4 w-4" />
      {variant !== 'compact' && <span className="hidden sm:inline">Help</span>}
      {showBadge && hasUncompletedRecommended && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-pulse" />
      )}
    </>
  );

  const buttonClass = cn(
    "relative gap-2",
    variant === 'floating' && "fixed bottom-6 right-6 z-40 shadow-lg",
    variant === 'compact' && "w-9 h-9 p-0",
    className
  );

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant === 'floating' ? 'default' : 'ghost'}
          size={variant === 'compact' ? 'icon' : 'sm'}
          className={buttonClass}
          aria-label="Help and tutorials"
        >
          {buttonContent}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end" 
        className="w-64"
        sideOffset={8}
      >
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Help & Tutorials</span>
          {completedCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {completedCount}/{tours.length} completed
            </Badge>
          )}
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />

        {/* Quick Start */}
        <DropdownMenuItem
          onClick={() => {
            triggerQuickStart();
            setIsOpen(false);
          }}
          className="flex items-center gap-3 cursor-pointer"
        >
          <RotateCcw className="h-4 w-4 text-muted-foreground" />
          <div className="flex-1">
            <div className="font-medium">Quick Start</div>
            <div className="text-xs text-muted-foreground">
              Quick refresher of key features
            </div>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Tour Options */}
        {tours.map((tour) => {
          const isCompleted = isFlowCompleted(tour.id);
          
          return (
            <DropdownMenuItem
              key={tour.id}
              onClick={() => {
                tour.trigger();
                setIsOpen(false);
              }}
              className="flex items-center gap-3 cursor-pointer"
              disabled={isCompleted}
            >
              <tour.icon 
                className={cn(
                  "h-4 w-4",
                  isCompleted ? "text-green-500" : "text-muted-foreground"
                )} 
              />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className={cn(
                    "font-medium",
                    isCompleted && "text-muted-foreground"
                  )}>
                    {tour.name}
                  </span>
                  {tour.recommended && !isCompleted && (
                    <Badge variant="secondary" className="text-xs">
                      Recommended
                    </Badge>
                  )}
                  {isCompleted && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  {tour.description}
                </div>
              </div>
            </DropdownMenuItem>
          );
        })}

        <DropdownMenuSeparator />

        {/* External Help */}
        <DropdownMenuItem className="flex items-center gap-3 cursor-pointer">
          <BookOpen className="h-4 w-4 text-muted-foreground" />
          <div className="flex-1">
            <div className="font-medium">Documentation</div>
            <div className="text-xs text-muted-foreground">
              View detailed guides and FAQ
            </div>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Floating help button for pages that need persistent help access
export function FloatingHelpButton() {
  return <HelpButton variant="floating" />;
}

// Compact help button for toolbars
export function CompactHelpButton({ className }: { className?: string }) {
  return <HelpButton variant="compact" className={className} />;
}
