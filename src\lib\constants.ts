// Application constants
export const APP_CONFIG = {
  name: "<PERSON><PERSON><PERSON><PERSON>",
  description: "AI-Powered Browser Extension Tutoring System",
  version: "1.0.0",
  author: "TutorAI Team",
} as const;

// Authentication constants
export const AUTH_CONFIG = {
  sessionStrategy: "database" as const,
  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
    forgotPassword: "/auth/forgot-password",
    error: "/auth/error",
  },
  redirects: {
    afterSignIn: "/dashboard",
    afterSignUp: "/dashboard",
    afterSignOut: "/",
  },
  validation: {
    passwordMinLength: 8,
    passwordMaxLength: 128,
    nameMinLength: 2,
    nameMaxLength: 50,
  },
} as const;

// Admin panel constants
export const ADMIN_CONFIG = {
  sidebar: {
    width: {
      expanded: 280,
      collapsed: 80,
    },
    navigation: [
      {
        id: "overview",
        label: "Overview",
        href: "/admin",
        icon: "BarChart3",
      },
      {
        id: "users",
        label: "Users",
        href: "/admin/user",
        icon: "Users",
      },
      {
        id: "roles",
        label: "Roles & Permissions",
        href: "/admin/roles",
        icon: "Shield",
      },
      {
        id: "tutorials",
        label: "Tutorials",
        href: "/admin/tutorials",
        icon: "BookOpen",
      },
      {
        id: "analytics",
        label: "Analytics",
        href: "/admin/analytics",
        icon: "TrendingUp",
      },
      {
        id: "settings",
        label: "Settings",
        href: "/admin/settings",
        icon: "Settings",
      },
    ],
  },
  permissions: {
    requiredRole: "ADMIN" as const,
    allowedRoles: ["ADMIN", "SUPER_ADMIN"] as const,
  },
} as const;

// Toast configuration
export const TOAST_CONFIG = {
  duration: {
    success: 4000,
    error: 6000,
    warning: 5000,
    info: 4000,
  },
  variants: {
    success: "default" as const,
    error: "destructive" as const,
    warning: "default" as const,
    info: "default" as const,
  },
} as const;

// API endpoints
export const API_ENDPOINTS = {
  auth: {
    signIn: "/api/auth/signin",
    signUp: "/api/auth/signup",
    signOut: "/api/auth/signout",
    session: "/api/auth/session",
    forgotPassword: "/api/auth/forgot-password",
    resetPassword: "/api/auth/reset-password",
  },
  admin: {
    users: "/api/admin/users",
    tutorials: "/api/tutorials",
    analytics: "/api/analytics",
  },
} as const;

// UI constants
export const UI_CONFIG = {
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    "2xl": 1536,
  },
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
    easing: {
      default: "cubic-bezier(0.4, 0, 0.2, 1)",
      in: "cubic-bezier(0.4, 0, 1, 1)",
      out: "cubic-bezier(0, 0, 0.2, 1)",
      inOut: "cubic-bezier(0.4, 0, 0.2, 1)",
    },
  },
} as const;
