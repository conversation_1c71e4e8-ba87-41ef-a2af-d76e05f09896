#!/usr/bin/env node

/**
 * Environment validation script
 * Run with: npm run validate-env
 */

import { z } from "zod";
import { readFileSync, existsSync } from "fs";
import { join } from "path";

// Color codes for console output
const colors = {
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
  white: "\x1b[37m",
  reset: "\x1b[0m",
  bright: "\x1b[1m",
};

function log(message: string, color: keyof typeof colors = "white") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message: string) {
  log(`❌ ${message}`, "red");
}

function logSuccess(message: string) {
  log(`✅ ${message}`, "green");
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, "yellow");
}

function logInfo(message: string) {
  log(`ℹ️  ${message}`, "blue");
}

// Environment validation schema
const envSchema = z.object({
  // Database (required)
  DATABASE_URL: z.string().url().optional(),
  DB_HOST: z.string().optional(),
  DB_PORT: z.string().optional(),
  DB_USER: z.string().optional(),
  DB_PASSWORD: z.string().min(1, "Database password is required").optional(),
  DB_NAME: z.string().optional(),

  // Authentication (required)
  NEXTAUTH_URL: z.string().url("NEXTAUTH_URL must be a valid URL"),
  NEXTAUTH_SECRET: z
    .string()
    .min(32, "NEXTAUTH_SECRET must be at least 32 characters"),

  // OAuth (optional)
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),

  // AI Providers (at least one required)
  OPENAI_API_KEY: z.string().optional(),
  ANTHROPIC_API_KEY: z.string().optional(),
  GROQ_API_KEY: z.string().optional(),
  OPENROUTER_API_KEY: z.string().optional(),
  GOOGLE_AI_API_KEY: z.string().optional(),

  // Email (recommended for production)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
  SMTP_FROM: z.string().email().optional(),

  // Application
  NODE_ENV: z
    .enum(["development", "staging", "production"])
    .default("development"),
  PORT: z.string().optional(),
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
});

function checkEnvFile() {
  const envPath = join(process.cwd(), ".env");
  const envExamplePath = join(process.cwd(), ".env.example");

  if (!existsSync(envPath)) {
    logError(".env file not found!");
    if (existsSync(envExamplePath)) {
      logInfo("Copy .env.example to .env and fill in your values:");
      logInfo("cp .env.example .env");
    }
    return false;
  }

  logSuccess(".env file found");
  return true;
}

function validateEnvironment() {
  log("\n🔍 Validating environment configuration...\n", "bright");

  // Check if .env file exists
  if (!checkEnvFile()) {
    process.exit(1);
  }

  try {
    // Parse environment variables with better error handling
    const env = envSchema.parse(process.env);

    // Check database configuration
    if (!env.DATABASE_URL && (!env.DB_HOST || !env.DB_PASSWORD)) {
      logError("Database configuration incomplete!");
      logInfo(
        "Either set DATABASE_URL or provide DB_HOST, DB_USER, DB_PASSWORD, and DB_NAME",
      );
      process.exit(1);
    }
    logSuccess("Database configuration valid");

    // Check authentication
    if (!env.NEXTAUTH_SECRET || env.NEXTAUTH_SECRET.length < 32) {
      logError("NEXTAUTH_SECRET is missing or too short (minimum 32 characters)");
      logInfo("Generate a secure secret: openssl rand -base64 32");
      logInfo("Example: node -e \"console.log(require('crypto').randomBytes(32).toString('base64'))\"");
      process.exit(1);
    }
    logSuccess("Authentication configuration valid");

    // Check AI providers
    const aiProviders = [
      env.OPENAI_API_KEY,
      env.ANTHROPIC_API_KEY,
      env.GROQ_API_KEY,
      env.OPENROUTER_API_KEY,
      env.GOOGLE_AI_API_KEY,
    ].filter(Boolean);

    if (aiProviders.length === 0) {
      if (env.NODE_ENV === "production") {
        logError("No AI providers configured!");
        logInfo("At least one AI provider API key is required");
        process.exit(1);
      } else {
        logWarning("No AI providers configured (some features may not work)");
      }
    } else {
      logSuccess(`${aiProviders.length} AI provider(s) configured`);
    }

    // Check email configuration
    const emailConfigured = env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASSWORD;
    if (!emailConfigured) {
      if (env.NODE_ENV === "production") {
        logWarning("Email not configured (password reset will not work)");
      } else {
        logInfo("Email not configured (emails will be logged in development)");
      }
    } else {
      logSuccess("Email configuration valid");
    }

    // Check OAuth
    const googleOAuthConfigured =
      env.GOOGLE_CLIENT_ID && env.GOOGLE_CLIENT_SECRET;
    if (googleOAuthConfigured) {
      logSuccess("Google OAuth configured");
    } else {
      logInfo("Google OAuth not configured (optional)");
    }

    // Environment-specific checks
    if (env.NODE_ENV === "production") {
      // Production-specific validations
      if (env.NEXTAUTH_URL?.includes("localhost")) {
        logError("NEXTAUTH_URL should not use localhost in production");
        process.exit(1);
      }

      if (!env.NEXT_PUBLIC_APP_URL) {
        logWarning("NEXT_PUBLIC_APP_URL not set (recommended for production)");
      }

      logSuccess("Production environment checks passed");
    }

    log("\n🎉 Environment validation completed successfully!\n", "green");

    // Display summary
    log("📋 Configuration Summary:", "bright");
    log(`   Environment: ${env.NODE_ENV}`);
    log(
      `   Database: ${env.DATABASE_URL ? "URL configured" : "Components configured"}`,
    );
    log(`   AI Providers: ${aiProviders.length} configured`);
    log(`   Email: ${emailConfigured ? "Configured" : "Not configured"}`);
    log(
      `   OAuth: ${googleOAuthConfigured ? "Google configured" : "Not configured"}`,
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      logError("Environment validation failed!");
      log("\n📝 Issues found:", "red");

      error.errors.forEach((err) => {
        const path = err.path.join(".");
        logError(`   ${path}: ${err.message}`);
      });

      log("\n💡 Tips:", "yellow");
      log("   - Check your .env file for missing or invalid values");
      log("   - Use .env.example as a reference");
      log("   - Generate secure secrets: openssl rand -base64 32");
    } else {
      logError(`Unexpected error: ${error}`);
    }

    process.exit(1);
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateEnvironment();
}

export { validateEnvironment };
