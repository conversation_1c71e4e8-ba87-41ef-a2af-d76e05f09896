"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Palette } from "lucide-react";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { useUserPreferences } from "@/hooks/useAccessibility";

interface ThemeSwitcherProps {
  variant?: "default" | "compact" | "icon-only";
  showLabel?: boolean;
  className?: string;
}

const ThemeSwitcher = ({
  variant = "default",
  showLabel = false,
  className
}: ThemeSwitcherProps) => {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme, resolvedTheme } = useTheme();
  const { reducedMotion } = useUserPreferences();

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="sm"
        className={cn("w-9 h-9", className)}
        disabled
        aria-label="Loading theme switcher"
      >
        <Palette className="h-4 w-4 animate-pulse" />
      </Button>
    );
  }

  const ICON_SIZE = variant === "compact" ? 14 : 16;

  const getThemeIcon = () => {
    const iconClass = cn(
      "transition-all duration-200",
      !reducedMotion && "group-hover:scale-110",
      resolvedTheme === "dark" ? "text-yellow-500" : "text-slate-600"
    );

    switch (theme) {
      case "light":
        return <Sun size={ICON_SIZE} className={iconClass} />;
      case "dark":
        return <Moon size={ICON_SIZE} className={iconClass} />;
      default:
        return <Laptop size={ICON_SIZE} className={iconClass} />;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case "light":
        return "Light mode";
      case "dark":
        return "Dark mode";
      default:
        return "System theme";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={variant === "compact" ? "sm" : "sm"}
          className={cn(
            "group relative",
            variant === "icon-only" ? "w-9 h-9 p-0" : "gap-2",
            className
          )}
          aria-label={`Current theme: ${getThemeLabel()}. Click to change theme.`}
        >
          {getThemeIcon()}
          {showLabel && variant !== "icon-only" && (
            <span className="hidden sm:inline text-xs">
              {theme === "system" ? "Auto" : theme === "light" ? "Light" : "Dark"}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-40"
        align="end"
        sideOffset={8}
      >
        <DropdownMenuRadioGroup
          value={theme}
          onValueChange={(newTheme) => {
            setTheme(newTheme);
            // Announce theme change for screen readers
            const themeNames = {
              light: "Light mode",
              dark: "Dark mode",
              system: "System theme"
            };
            // This would be announced by screen readers
            setTimeout(() => {
              const announcement = document.createElement('div');
              announcement.setAttribute('aria-live', 'polite');
              announcement.className = 'sr-only';
              announcement.textContent = `Switched to ${themeNames[newTheme as keyof typeof themeNames]}`;
              document.body.appendChild(announcement);
              setTimeout(() => document.body.removeChild(announcement), 1000);
            }, 100);
          }}
        >
          <DropdownMenuRadioItem
            className="flex gap-3 cursor-pointer"
            value="light"
          >
            <Sun
              size={ICON_SIZE}
              className={cn(
                "transition-colors",
                theme === "light" ? "text-yellow-500" : "text-muted-foreground"
              )}
            />
            <span>Light</span>
          </DropdownMenuRadioItem>
          <DropdownMenuRadioItem
            className="flex gap-3 cursor-pointer"
            value="dark"
          >
            <Moon
              size={ICON_SIZE}
              className={cn(
                "transition-colors",
                theme === "dark" ? "text-blue-400" : "text-muted-foreground"
              )}
            />
            <span>Dark</span>
          </DropdownMenuRadioItem>
          <DropdownMenuRadioItem
            className="flex gap-3 cursor-pointer"
            value="system"
          >
            <Laptop
              size={ICON_SIZE}
              className={cn(
                "transition-colors",
                theme === "system" ? "text-primary" : "text-muted-foreground"
              )}
            />
            <span>System</span>
          </DropdownMenuRadioItem>
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export { ThemeSwitcher };
