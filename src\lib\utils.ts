import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Responsive utilities for consistent breakpoint handling
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

// Responsive container classes
export const containerClasses = {
  base: "w-full mx-auto px-4 sm:px-6 lg:px-8",
  sm: "max-w-screen-sm",
  md: "max-w-screen-md",
  lg: "max-w-screen-lg",
  xl: "max-w-screen-xl",
  "2xl": "max-w-screen-2xl",
  full: "max-w-full",
} as const;

// Responsive grid utilities
export const gridClasses = {
  responsive: {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
    6: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6",
  },
  gap: {
    sm: "gap-2 sm:gap-3 md:gap-4",
    md: "gap-3 sm:gap-4 md:gap-6",
    lg: "gap-4 sm:gap-6 md:gap-8",
  },
} as const;

// Responsive spacing utilities
export const spacingClasses = {
  section: "py-8 sm:py-12 md:py-16 lg:py-20",
  container: "px-4 sm:px-6 lg:px-8",
  card: "p-4 sm:p-6 lg:p-8",
  button: "px-4 py-2 sm:px-6 sm:py-3",
} as const;

// Responsive text utilities
export const textClasses = {
  heading: {
    h1: "text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight",
    h2: "text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight",
    h3: "text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold",
    h4: "text-base sm:text-lg md:text-xl font-semibold",
  },
  body: {
    sm: "text-xs sm:text-sm",
    base: "text-sm sm:text-base",
    lg: "text-base sm:text-lg md:text-xl",
  },
} as const;

// Mobile-first responsive helper
export function responsive(classes: Record<Breakpoint, string>): string {
  return Object.entries(classes)
    .map(([breakpoint, className]) => {
      if (breakpoint === 'xs') return className;
      return `${breakpoint}:${className}`;
    })
    .join(' ');
}

// Check if current screen size matches breakpoint
export function useBreakpoint() {
  if (typeof window === 'undefined') return 'lg'; // SSR fallback

  const width = window.innerWidth;

  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'xs';
}
