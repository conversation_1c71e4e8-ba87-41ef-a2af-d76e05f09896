import React from "react";
import { cn, containerClasses, spacingClasses } from "@/lib/utils";

interface ResponsiveContainerProps {
  children: React.ReactNode;
  size?: keyof typeof containerClasses;
  padding?: boolean;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export function ResponsiveContainer({
  children,
  size = "xl",
  padding = true,
  className,
  as: Component = "div",
}: ResponsiveContainerProps) {
  return (
    <Component
      className={cn(
        containerClasses.base,
        size !== "full" && containerClasses[size],
        padding && spacingClasses.container,
        className
      )}
    >
      {children}
    </Component>
  );
}

interface ResponsiveSectionProps {
  children: React.ReactNode;
  className?: string;
  containerSize?: keyof typeof containerClasses;
  spacing?: "sm" | "md" | "lg";
  background?: "default" | "muted" | "accent";
  as?: keyof JSX.IntrinsicElements;
}

export function ResponsiveSection({
  children,
  className,
  containerSize = "xl",
  spacing = "md",
  background = "default",
  as: Component = "section",
}: ResponsiveSectionProps) {
  const backgroundClasses = {
    default: "",
    muted: "bg-muted/30",
    accent: "bg-accent/5",
  };

  const spacingMap = {
    sm: "py-8 sm:py-12",
    md: "py-12 sm:py-16 md:py-20",
    lg: "py-16 sm:py-20 md:py-24 lg:py-32",
  };

  return (
    <Component
      className={cn(
        spacingMap[spacing],
        backgroundClasses[background],
        className
      )}
    >
      <ResponsiveContainer size={containerSize}>
        {children}
      </ResponsiveContainer>
    </Component>
  );
}

interface ResponsiveGridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4 | 6;
  gap?: "sm" | "md" | "lg";
  className?: string;
}

export function ResponsiveGrid({
  children,
  cols = 3,
  gap = "md",
  className,
}: ResponsiveGridProps) {
  const colsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
    6: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6",
  };

  const gapClasses = {
    sm: "gap-3 sm:gap-4",
    md: "gap-4 sm:gap-6",
    lg: "gap-6 sm:gap-8",
  };

  return (
    <div
      className={cn(
        "grid",
        colsClasses[cols],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}

interface ResponsiveStackProps {
  children: React.ReactNode;
  direction?: "vertical" | "horizontal";
  gap?: "sm" | "md" | "lg";
  align?: "start" | "center" | "end";
  justify?: "start" | "center" | "end" | "between";
  wrap?: boolean;
  className?: string;
}

export function ResponsiveStack({
  children,
  direction = "vertical",
  gap = "md",
  align = "start",
  justify = "start",
  wrap = false,
  className,
}: ResponsiveStackProps) {
  const directionClasses = {
    vertical: "flex-col",
    horizontal: "flex-row",
  };

  const gapClasses = {
    sm: direction === "vertical" ? "space-y-2 sm:space-y-3" : "space-x-2 sm:space-x-3",
    md: direction === "vertical" ? "space-y-4 sm:space-y-6" : "space-x-4 sm:space-x-6",
    lg: direction === "vertical" ? "space-y-6 sm:space-y-8" : "space-x-6 sm:space-x-8",
  };

  const alignClasses = {
    start: "items-start",
    center: "items-center",
    end: "items-end",
  };

  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between",
  };

  return (
    <div
      className={cn(
        "flex",
        directionClasses[direction],
        gapClasses[gap],
        alignClasses[align],
        justifyClasses[justify],
        wrap && "flex-wrap",
        className
      )}
    >
      {children}
    </div>
  );
}
