import { TempoInit } from "@/components/tempo-init";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { ClientSessionProvider } from "@/components/providers/session-provider";
import { Toaster } from "@/components/ui/toaster";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { OnboardingManager } from "@/components/onboarding/OnboardingManager";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import Script from "next/script";
import { getServerSession } from "next-auth/next";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TutorAI - AI-Powered Browser Extension Tutoring System",
  description:
    "Real-time AI explanations, voice synthesis, and interactive guidance for any webpage. Transform how you learn and navigate the web with intelligent assistance.",
  keywords:
    "AI tutoring, browser extension, learning, education, voice synthesis, interactive guidance",
  authors: [{ name: "TutorAI Team" }],
  openGraph: {
    title: "TutorAI - AI-Powered Browser Extension Tutoring System",
    description:
      "Transform how you learn and navigate the web with intelligent AI assistance.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "TutorAI - AI-Powered Browser Extension Tutoring System",
    description:
      "Transform how you learn and navigate the web with intelligent AI assistance.",
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await getServerSession();

  return (
    <html lang="en" suppressHydrationWarning>
      <Script src="https://api.tempo.new/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js" />
      <body className={inter.className}>
        <ErrorBoundary>
          <ClientSessionProvider session={session}>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange={false}
              storageKey="tutorai-theme"
              themes={['light', 'dark', 'system']}
            >
              <OnboardingManager>
                <ErrorBoundary>
                  {children}
                </ErrorBoundary>
                <Toaster />
                <TempoInit />
              </OnboardingManager>
            </ThemeProvider>
          </ClientSessionProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
