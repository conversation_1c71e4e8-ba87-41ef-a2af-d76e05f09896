import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { 
  AlertTriangle, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  Server, 
  Shield, 
  Clock,
  FileX,
  Search,
  UserX,
  Lock
} from "lucide-react";

interface BaseErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  retryText?: string;
  className?: string;
  variant?: "default" | "card" | "inline";
}

function BaseError({ 
  title, 
  message, 
  onRetry, 
  retryText = "Try Again",
  className,
  variant = "default",
  icon: Icon,
  children
}: BaseErrorProps & { 
  icon: React.ComponentType<{ className?: string }>;
  children?: React.ReactNode;
}) {
  const content = (
    <>
      <div className="flex flex-col items-center text-center space-y-4">
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
          <Icon className="h-6 w-6 text-destructive" />
        </div>
        {title && <h3 className="text-lg font-semibold">{title}</h3>}
        {message && <p className="text-muted-foreground max-w-md">{message}</p>}
        {children}
        {onRetry && (
          <Button onClick={onRetry} className="gap-2">
            <RefreshCw className="h-4 w-4" />
            {retryText}
          </Button>
        )}
      </div>
    </>
  );

  if (variant === "card") {
    return (
      <Card className={cn("w-full max-w-md mx-auto", className)}>
        <CardContent className="p-6">
          {content}
        </CardContent>
      </Card>
    );
  }

  if (variant === "inline") {
    return (
      <Alert variant="destructive" className={cn("max-w-md", className)}>
        <Icon className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription className="mt-2 space-y-2">
          {message}
          {onRetry && (
            <Button onClick={onRetry} size="sm" variant="outline" className="gap-2">
              <RefreshCw className="h-4 w-4" />
              {retryText}
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={cn("flex items-center justify-center p-8", className)}>
      {content}
    </div>
  );
}

// Network/Connection Errors
export function NetworkError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={WifiOff}
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection."
      {...props}
    />
  );
}

export function ServerError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={Server}
      title="Server Error"
      message="The server is currently unavailable. Please try again later."
      {...props}
    />
  );
}

export function TimeoutError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={Clock}
      title="Request Timeout"
      message="The request took too long to complete. Please try again."
      {...props}
    />
  );
}

// Authentication/Authorization Errors
export function UnauthorizedError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={Lock}
      title="Access Denied"
      message="You don't have permission to access this resource."
      retryText="Sign In"
      {...props}
    />
  );
}

export function ForbiddenError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={Shield}
      title="Forbidden"
      message="You don't have permission to perform this action."
      {...props}
    />
  );
}

// Data/Content Errors
export function NotFoundError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={FileX}
      title="Not Found"
      message="The requested resource could not be found."
      retryText="Go Back"
      {...props}
    />
  );
}

export function NoResultsError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={Search}
      title="No Results Found"
      message="We couldn't find any results matching your criteria."
      retryText="Clear Filters"
      {...props}
    />
  );
}

// Generic Errors
export function GenericError(props: BaseErrorProps) {
  return (
    <BaseError
      icon={AlertTriangle}
      title="Something went wrong"
      message="An unexpected error occurred. Please try again."
      {...props}
    />
  );
}

// API Error Handler Component
interface ApiErrorProps extends BaseErrorProps {
  error: {
    status?: number;
    message?: string;
    code?: string;
  };
}

export function ApiError({ error, ...props }: ApiErrorProps) {
  const getErrorComponent = () => {
    switch (error.status) {
      case 401:
        return UnauthorizedError;
      case 403:
        return ForbiddenError;
      case 404:
        return NotFoundError;
      case 408:
        return TimeoutError;
      case 500:
      case 502:
      case 503:
      case 504:
        return ServerError;
      default:
        if (!navigator.onLine) {
          return NetworkError;
        }
        return GenericError;
    }
  };

  const ErrorComponent = getErrorComponent();
  
  return (
    <ErrorComponent
      message={error.message || props.message}
      {...props}
    />
  );
}

// Error Boundary Fallback with different error types
interface ErrorBoundaryFallbackProps {
  error: Error;
  resetError: () => void;
  className?: string;
}

export function ErrorBoundaryFallback({ 
  error, 
  resetError, 
  className 
}: ErrorBoundaryFallbackProps) {
  // Determine error type based on error message/properties
  const getErrorType = () => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return NetworkError;
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return UnauthorizedError;
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ForbiddenError;
    }
    if (message.includes('not found') || message.includes('404')) {
      return NotFoundError;
    }
    if (message.includes('timeout')) {
      return TimeoutError;
    }
    if (message.includes('server') || message.includes('500')) {
      return ServerError;
    }
    
    return GenericError;
  };

  const ErrorComponent = getErrorType();
  
  return (
    <ErrorComponent
      message={error.message}
      onRetry={resetError}
      className={className}
      variant="card"
    />
  );
}

// Async Operation Error Handler
interface AsyncErrorProps {
  error: string | null;
  loading: boolean;
  onRetry?: () => void;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

export function AsyncError({ 
  error, 
  loading, 
  onRetry, 
  children, 
  fallback,
  className 
}: AsyncErrorProps) {
  if (loading) {
    return <>{children}</>;
  }

  if (error) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <GenericError
        message={error}
        onRetry={onRetry}
        className={className}
        variant="inline"
      />
    );
  }

  return <>{children}</>;
}

// Form Error Display
interface FormErrorProps {
  errors: Record<string, string[]> | string[];
  className?: string;
}

export function FormError({ errors, className }: FormErrorProps) {
  const errorList = Array.isArray(errors) ? errors : Object.values(errors).flat();
  
  if (errorList.length === 0) return null;

  return (
    <Alert variant="destructive" className={cn("mb-4", className)}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Please fix the following errors:</AlertTitle>
      <AlertDescription>
        <ul className="list-disc list-inside space-y-1 mt-2">
          {errorList.map((error, index) => (
            <li key={index} className="text-sm">{error}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
