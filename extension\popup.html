<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TutorAI</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <!-- Header -->
    <div class="header">
      <img src="icons/icon-32.png" alt="TutorAI" class="logo">
      <h1>TutorAI</h1>
      <div class="status-indicator" id="status-indicator">
        <span class="status-dot"></span>
        <span class="status-text">Connecting...</span>
      </div>
    </div>

    <!-- Main Content -->
    <div class="content">
      <!-- Quick Actions -->
      <div class="section">
        <h3>Quick Actions</h3>
        <div class="action-buttons">
          <button id="start-tutorial" class="btn btn-primary">
            <span class="icon">🎓</span>
            Start Tutorial
          </button>
          <button id="explain-page" class="btn btn-secondary">
            <span class="icon">📖</span>
            Explain Page
          </button>
          <button id="toggle-highlighting" class="btn btn-secondary">
            <span class="icon">✨</span>
            <span id="highlight-text">Enable Highlighting</span>
          </button>
        </div>
      </div>

      <!-- Current Page Info -->
      <div class="section">
        <h3>Current Page</h3>
        <div class="page-info">
          <div class="page-title" id="page-title">Loading...</div>
          <div class="page-url" id="page-url">Loading...</div>
        </div>
      </div>

      <!-- AI Response -->
      <div class="section" id="response-section" style="display: none;">
        <h3>AI Response</h3>
        <div class="ai-response" id="ai-response">
          <div class="response-text" id="response-text"></div>
          <div class="response-actions">
            <button id="clear-response" class="btn btn-small">Clear</button>
          </div>
        </div>
      </div>

      <!-- Settings -->
      <div class="section">
        <h3>Settings</h3>
        <div class="settings">
          <label class="setting-item">
            <input type="checkbox" id="auto-start" />
            <span>Auto-start on new pages</span>
          </label>
          <label class="setting-item">
            <input type="checkbox" id="voice-enabled" />
            <span>Enable voice responses</span>
          </label>
          <div class="setting-item">
            <label for="highlight-color">Highlight Color:</label>
            <input type="color" id="highlight-color" value="#3b82f6" />
          </div>
          <div class="setting-actions">
            <button id="advanced-settings" class="btn btn-secondary btn-small">Advanced</button>
            <button id="backup-settings" class="btn btn-secondary btn-small">Backup</button>
            <button id="performance-dashboard" class="btn btn-secondary btn-small">Performance</button>
            <button id="error-dashboard" class="btn btn-secondary btn-small">Errors</button>
            <button id="reset-settings" class="btn btn-text btn-small">Reset</button>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <div class="links">
          <a href="#" id="open-dashboard">Dashboard</a>
          <a href="#" id="help">Help</a>
          <a href="#" id="feedback">Feedback</a>
        </div>
        <div class="version">v1.0.0</div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
      <div class="spinner"></div>
      <div class="loading-text">Processing...</div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
