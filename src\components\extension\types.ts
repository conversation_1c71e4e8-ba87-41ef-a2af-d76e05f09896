export interface TutorialStep {
  id: string;
  selector: string;
  title: string;
  description: string;
  position?: TooltipPosition;
  highlightStyle?: HighlightStyle;
  action?: StepAction;
}

export interface TooltipPosition {
  placement: "top" | "bottom" | "left" | "right" | "auto";
  offset?: { x: number; y: number };
  align?: "start" | "center" | "end";
}

export interface HighlightStyle {
  borderColor?: string;
  borderWidth?: number;
  shadowColor?: string;
  shadowBlur?: number;
  pulseAnimation?: boolean;
  backgroundColor?: string;
  borderRadius?: number;
}

export interface StepAction {
  type: "click" | "hover" | "focus" | "scroll" | "wait";
  target?: string;
  duration?: number;
}

export interface HighlightingConfig {
  theme: "light" | "dark" | "auto";
  animationDuration: number;
  showProgress: boolean;
  allowSkip: boolean;
  keyboardNavigation: boolean;
  autoAdvance: boolean;
  autoAdvanceDelay: number;
  zIndexBase: number;
}

export interface ElementBounds {
  top: number;
  left: number;
  width: number;
  height: number;
  right: number;
  bottom: number;
}

export interface ViewportBounds {
  width: number;
  height: number;
  scrollX: number;
  scrollY: number;
}

export interface TooltipBounds {
  width: number;
  height: number;
}

export interface CalculatedPosition {
  x: number;
  y: number;
  placement: TooltipPosition["placement"];
  arrow: {
    x: number;
    y: number;
    side: "top" | "bottom" | "left" | "right";
  };
}

export interface NavigationState {
  currentStep: number;
  totalSteps: number;
  isPlaying: boolean;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

export interface HighlightingSystemProps {
  steps: TutorialStep[];
  config?: Partial<HighlightingConfig>;
  onStepChange?: (step: number, stepData: TutorialStep) => void;
  onComplete?: () => void;
  onSkip?: () => void;
  onClose?: () => void;
  isActive?: boolean;
  initialStep?: number;
}

export interface TooltipComponentProps {
  step: TutorialStep;
  position: CalculatedPosition;
  isVisible: boolean;
  theme: HighlightingConfig["theme"];
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
  onClose: () => void;
  navigationState: NavigationState;
  animationDuration: number;
}

export interface NavigationControlsProps {
  navigationState: NavigationState;
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
  onClose: () => void;
  allowSkip: boolean;
  theme: HighlightingConfig["theme"];
}

export interface ProgressIndicatorProps {
  current: number;
  total: number;
  theme: HighlightingConfig["theme"];
}

export interface UseHighlightingOptions {
  steps: TutorialStep[];
  config: HighlightingConfig;
  onStepChange?: (step: number, stepData: TutorialStep) => void;
  onComplete?: () => void;
}

export interface UseHighlightingReturn {
  currentStep: number;
  currentStepData: TutorialStep | null;
  highlightedElement: Element | null;
  tooltipPosition: CalculatedPosition | null;
  navigationState: NavigationState;
  isActive: boolean;
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  skipTutorial: () => void;
  closeTutorial: () => void;
  startTutorial: () => void;
}
